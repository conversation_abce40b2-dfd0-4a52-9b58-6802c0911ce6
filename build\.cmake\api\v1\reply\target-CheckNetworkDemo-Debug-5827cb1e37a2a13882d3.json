{"artifacts": [{"path": "CheckNetworkDemo.exe"}, {"path": "CheckNetworkDemo.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_compile_options", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 46, "parent": 0}, {"command": 1, "file": 0, "line": 62, "parent": 0}, {"command": 2, "file": 0, "line": 47, "parent": 0}, {"command": 2, "file": 0, "line": 43, "parent": 0}, {"command": 3, "file": 0, "line": 15, "parent": 0}, {"command": 4, "file": 0, "line": 23, "parent": 0}, {"command": 5, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}, {"backtrace": 5, "fragment": "-Wall"}, {"backtrace": 5, "fragment": "-Wextra"}, {"backtrace": 5, "fragment": "-pedantic"}], "defines": [{"backtrace": 6, "define": "WIN32_LEAN_AND_MEAN"}], "includes": [{"backtrace": 7, "path": "E:/workspace/vscode/CheckNetwork/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "CheckNetwork::@6890427a1f51a3e7e1df"}], "id": "CheckNetworkDemo::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/CheckNetwork"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 3, "fragment": "libCheckNetwork.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 4, "fragment": "-<PERSON><PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 4, "fragment": "-l<PERSON><PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 4, "fragment": "-lole32", "role": "libraries"}, {"backtrace": 4, "fragment": "-loleaut32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "CheckNetworkDemo", "nameOnDisk": "CheckNetworkDemo.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}