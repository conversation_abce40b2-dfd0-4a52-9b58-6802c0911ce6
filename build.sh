#!/bin/bash

# Linux构建脚本

echo "===== CheckNetwork Linux Build Script ====="

# 检查CMake是否安装
if ! command -v cmake &> /dev/null; then
    echo "Error: CMake not found. Please install CMake first."
    echo "Ubuntu/Debian: sudo apt-get install cmake"
    echo "CentOS/RHEL: sudo yum install cmake"
    echo "Fedora: sudo dnf install cmake"
    exit 1
fi

# 检查编译器
if ! command -v g++ &> /dev/null; then
    echo "Error: g++ not found. Please install g++ first."
    echo "Ubuntu/Debian: sudo apt-get install build-essential"
    echo "CentOS/RHEL: sudo yum groupinstall 'Development Tools'"
    echo "Fedora: sudo dnf groupinstall 'Development Tools'"
    exit 1
fi

# 检查g++版本
GCC_VERSION=$(g++ -dumpversion | cut -d. -f1)
if [ "$GCC_VERSION" -lt 7 ]; then
    echo "Warning: g++ version $GCC_VERSION may not fully support C++17."
    echo "Recommended: g++ 7.0 or higher"
fi

# 创建构建目录
mkdir -p build
cd build

echo ""
echo "===== Configuring project ====="
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed."
    exit 1
fi

echo ""
echo "===== Building project ====="
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "Error: Build failed."
    exit 1
fi

echo ""
echo "===== Build completed successfully! ====="
echo ""
echo "Executables location:"
echo "  Demo: build/CheckNetworkDemo"
echo "  Tests: build/tests/CheckNetworkTests (if Google Test is available)"
echo ""

# 检查是否有测试可执行文件
if [ -f "tests/CheckNetworkTests" ]; then
    echo "===== Running tests ====="
    ctest --verbose
    echo ""
fi

echo "To run the demo:"
echo "  ./CheckNetworkDemo --help"
echo ""
echo "To install system-wide (optional):"
echo "  sudo make install"
echo ""
