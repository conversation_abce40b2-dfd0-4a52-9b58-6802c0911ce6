# 测试CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# 查找Google Test
find_package(GTest REQUIRED)

# 包含目录
include_directories(../include)

# 测试源文件
set(TEST_SOURCES
    test_NetworkAdapter.cpp
    test_main.cpp
)

# 创建测试可执行文件
add_executable(CheckNetworkTests ${TEST_SOURCES})

# 链接库
target_link_libraries(CheckNetworkTests 
    CheckNetwork
    GTest::gtest
    GTest::gtest_main
    ${PLATFORM_LIBS}
)

# 添加测试
add_test(NAME NetworkAdapterTests COMMAND CheckNetworkTests)

# 设置测试属性
set_tests_properties(NetworkAdapterTests PROPERTIES
    TIMEOUT 30
)
