# Test CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# Include directories
include_directories(../include)

# Simple test executable (without Google Test dependency)
add_executable(SimpleNetworkTest simple_test.cpp)
target_link_libraries(SimpleNetworkTest CheckNetwork ${PLATFORM_LIBS})

# Add simple test
add_test(NAME SimpleNetworkTest COMMAND SimpleNetworkTest)

# Google Test (optional)
find_package(GTest QUIET)
if(GTest_FOUND)
    # Test source files
    set(TEST_SOURCES
        test_NetworkAdapter.cpp
        test_main.cpp
    )

    # Create test executable
    add_executable(CheckNetworkTests ${TEST_SOURCES})

    # Link libraries
    target_link_libraries(CheckNetworkTests
        CheckNetwork
        GTest::gtest
        GTest::gtest_main
        ${PLATFORM_LIBS}
    )

    # Add test
    add_test(NAME NetworkAdapterTests COMMAND CheckNetworkTests)

    # Set test properties
    set_tests_properties(NetworkAdapterTests PROPERTIES
        TIMEOUT 30
    )
else()
    message(STATUS "Google Test not found. Only simple tests will be available.")
endif()
