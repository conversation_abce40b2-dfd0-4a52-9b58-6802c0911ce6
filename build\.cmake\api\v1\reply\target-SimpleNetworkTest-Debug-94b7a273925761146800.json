{"artifacts": [{"path": "tests/SimpleNetworkTest.exe"}, {"path": "tests/SimpleNetworkTest.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_compile_options", "add_definitions", "include_directories"], "files": ["tests/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 8, "parent": 0}, {"command": 1, "file": 0, "line": 9, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 15, "parent": 3}, {"command": 3, "file": 1, "line": 23, "parent": 3}, {"command": 4, "file": 1, "line": 19, "parent": 3}, {"command": 4, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wextra"}, {"backtrace": 4, "fragment": "-pedantic"}], "defines": [{"backtrace": 5, "define": "WIN32_LEAN_AND_MEAN"}], "includes": [{"backtrace": 6, "path": "E:/workspace/vscode/CheckNetwork/include"}, {"backtrace": 7, "path": "E:/workspace/vscode/CheckNetwork/tests/../include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "CheckNetwork::@6890427a1f51a3e7e1df"}], "id": "SimpleNetworkTest::@a44f0ac069e85531cdee", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "..\\libCheckNetwork.a", "role": "libraries"}, {"backtrace": 2, "fragment": "-lws2_32", "role": "libraries"}, {"backtrace": 2, "fragment": "-<PERSON><PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-l<PERSON><PERSON><PERSON><PERSON>", "role": "libraries"}, {"backtrace": 2, "fragment": "-lole32", "role": "libraries"}, {"backtrace": 2, "fragment": "-loleaut32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "SimpleNetworkTest", "nameOnDisk": "SimpleNetworkTest.exe", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "tests/simple_test.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}