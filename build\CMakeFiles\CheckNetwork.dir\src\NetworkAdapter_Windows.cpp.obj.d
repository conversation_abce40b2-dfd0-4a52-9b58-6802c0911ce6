CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj: \
 E:\workspace\vscode\CheckNetwork\src\NetworkAdapter_Windows.cpp \
 E:/workspace/vscode/CheckNetwork/include/NetworkAdapter.h \
 D:/msys64/mingw64/include/c++/15.1.0/string \
 D:/msys64/mingw64/include/c++/15.1.0/bits/requires_hosted.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/c++config.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/os_defines.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/cpu_defines.h \
 D:/msys64/mingw64/include/c++/15.1.0/pstl/pstl_config.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stringfwd.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/memoryfwd.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/char_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/postypes.h \
 D:/msys64/mingw64/include/c++/15.1.0/cwchar \
 D:/msys64/mingw64/include/wchar.h D:/msys64/mingw64/include/corecrt.h \
 D:/msys64/mingw64/include/_mingw.h \
 D:/msys64/mingw64/include/_mingw_mac.h \
 D:/msys64/mingw64/include/_mingw_secapi.h \
 D:/msys64/mingw64/include/vadefs.h \
 D:/msys64/mingw64/include/sdks/_mingw_ddk.h \
 D:/msys64/mingw64/include/corecrt_stdio_config.h \
 D:/msys64/mingw64/include/corecrt_wstdlib.h \
 D:/msys64/mingw64/include/corecrt_wctype.h \
 D:/msys64/mingw64/include/_mingw_off_t.h \
 D:/msys64/mingw64/include/_mingw_stat64.h \
 D:/msys64/mingw64/include/swprintf.inl \
 D:/msys64/mingw64/include/sec_api/wchar_s.h \
 D:/msys64/mingw64/include/c++/15.1.0/type_traits \
 D:/msys64/mingw64/include/c++/15.1.0/bits/version.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/allocator.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/c++allocator.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/new_allocator.h \
 D:/msys64/mingw64/include/c++/15.1.0/new \
 D:/msys64/mingw64/include/c++/15.1.0/bits/exception.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/functexcept.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/exception_defines.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/move.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/cpp_type_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/localefwd.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/c++locale.h \
 D:/msys64/mingw64/include/c++/15.1.0/clocale \
 D:/msys64/mingw64/include/locale.h D:/msys64/mingw64/include/crtdefs.h \
 D:/msys64/mingw64/include/stdio.h \
 D:/msys64/mingw64/include/sec_api/stdio_s.h \
 D:/msys64/mingw64/include/c++/15.1.0/iosfwd \
 D:/msys64/mingw64/include/c++/15.1.0/cctype \
 D:/msys64/mingw64/include/ctype.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/ostream_insert.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/cxxabi_forced.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_iterator_base_funcs.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/concept_check.h \
 D:/msys64/mingw64/include/c++/15.1.0/debug/assertions.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_iterator_base_types.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_iterator.h \
 D:/msys64/mingw64/include/c++/15.1.0/ext/type_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/ptr_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_function.h \
 D:/msys64/mingw64/include/c++/15.1.0/backward/binders.h \
 D:/msys64/mingw64/include/c++/15.1.0/ext/numeric_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_algobase.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_pair.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/utility.h \
 D:/msys64/mingw64/include/c++/15.1.0/debug/debug.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/predefined_ops.h \
 D:/msys64/mingw64/include/c++/15.1.0/bit \
 D:/msys64/mingw64/include/c++/15.1.0/concepts \
 D:/msys64/mingw64/include/c++/15.1.0/bits/refwrap.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/invoke.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/range_access.h \
 D:/msys64/mingw64/include/c++/15.1.0/initializer_list \
 D:/msys64/mingw64/include/c++/15.1.0/bits/basic_string.h \
 D:/msys64/mingw64/include/c++/15.1.0/ext/alloc_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/alloc_traits.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_construct.h \
 D:/msys64/mingw64/include/c++/15.1.0/string_view \
 D:/msys64/mingw64/include/c++/15.1.0/bits/functional_hash.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/hash_bytes.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/string_view.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/ext/string_conversions.h \
 D:/msys64/mingw64/include/c++/15.1.0/cstdlib \
 D:/msys64/mingw64/include/stdlib.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h \
 D:/msys64/mingw64/include/limits.h \
 D:/msys64/mingw64/include/sec_api/stdlib_s.h \
 D:/msys64/mingw64/include/c++/15.1.0/stdlib.h \
 D:/msys64/mingw64/include/malloc.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h \
 D:/msys64/mingw64/include/errno.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/std_abs.h \
 D:/msys64/mingw64/include/c++/15.1.0/cstdio \
 D:/msys64/mingw64/include/c++/15.1.0/cerrno \
 D:/msys64/mingw64/include/c++/15.1.0/bits/charconv.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/basic_string.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/bits/memory_resource.h \
 D:/msys64/mingw64/include/c++/15.1.0/cstddef \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h \
 D:/msys64/mingw64/include/stddef.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/uses_allocator.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/uses_allocator_args.h \
 D:/msys64/mingw64/include/c++/15.1.0/tuple \
 D:/msys64/mingw64/include/c++/15.1.0/vector \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_uninitialized.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_vector.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_bvector.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/vector.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/memory \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_tempbuf.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/stl_raw_storage_iter.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/align.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/unique_ptr.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/shared_ptr.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/shared_ptr_base.h \
 D:/msys64/mingw64/include/c++/15.1.0/typeinfo \
 D:/msys64/mingw64/include/c++/15.1.0/bits/allocated_ptr.h \
 D:/msys64/mingw64/include/c++/15.1.0/ext/aligned_buffer.h \
 D:/msys64/mingw64/include/c++/15.1.0/ext/atomicity.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/gthr.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/gthr-default.h \
 D:/msys64/mingw64/include/pthread.h \
 D:/msys64/mingw64/include/sys/types.h \
 D:/msys64/mingw64/include/process.h \
 D:/msys64/mingw64/include/corecrt_startup.h \
 D:/msys64/mingw64/include/signal.h \
 D:/msys64/mingw64/include/pthread_signal.h \
 D:/msys64/mingw64/include/time.h D:/msys64/mingw64/include/sys/timeb.h \
 D:/msys64/mingw64/include/sec_api/sys/timeb_s.h \
 D:/msys64/mingw64/include/_timeval.h \
 D:/msys64/mingw64/include/pthread_time.h \
 D:/msys64/mingw64/include/pthread_compat.h \
 D:/msys64/mingw64/include/sched.h \
 D:/msys64/mingw64/include/pthread_unistd.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/atomic_word.h \
 D:/msys64/mingw64/include/c++/15.1.0/ext/concurrence.h \
 D:/msys64/mingw64/include/c++/15.1.0/exception \
 D:/msys64/mingw64/include/c++/15.1.0/bits/exception_ptr.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/cxxabi_init_exception.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/nested_exception.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/shared_ptr_atomic.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/atomic_base.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/atomic_lockfree_defines.h \
 D:/msys64/mingw64/include/c++/15.1.0/backward/auto_ptr.h \
 D:/msys64/mingw64/include/c++/15.1.0/pstl/glue_memory_defs.h \
 D:/msys64/mingw64/include/c++/15.1.0/pstl/execution_defs.h \
 D:/msys64/mingw64/include/windows.h \
 D:/msys64/mingw64/include/sdkddkver.h D:/msys64/mingw64/include/excpt.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stdarg.h \
 D:/msys64/mingw64/include/stdarg.h \
 D:/msys64/mingw64/include/_mingw_stdarg.h \
 D:/msys64/mingw64/include/windef.h \
 D:/msys64/mingw64/include/winapifamily.h \
 D:/msys64/mingw64/include/minwindef.h \
 D:/msys64/mingw64/include/specstrings.h D:/msys64/mingw64/include/sal.h \
 D:/msys64/mingw64/include/concurrencysal.h \
 D:/msys64/mingw64/include/driverspecs.h \
 D:/msys64/mingw64/include/winnt.h \
 D:/msys64/mingw64/include/_mingw_unicode.h \
 D:/msys64/mingw64/include/apiset.h \
 D:/msys64/mingw64/include/psdk_inc/intrin-impl.h \
 D:/msys64/mingw64/include/basetsd.h D:/msys64/mingw64/include/guiddef.h \
 D:/msys64/mingw64/include/string.h \
 D:/msys64/mingw64/include/sec_api/string_s.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/x86intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/x86gprintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/ia32intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/adxintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/bmiintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/bmi2intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/cetintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/cldemoteintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/clflushoptintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/clwbintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/clzerointrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/cmpccxaddintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/enqcmdintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/fxsrintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/lzcntintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/lwpintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/movdirintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mwaitintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mwaitxintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/pconfigintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/popcntintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/pkuintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/prfchiintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/raointintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/rdseedintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/rtmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/serializeintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/sgxintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/tbmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/tsxldtrkintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/uintrintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/waitpkgintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/wbnoinvdintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xsaveintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xsavecintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xsaveoptintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xsavesintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xtestintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/hresetintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/usermsrintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/immintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xmmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/emmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/pmmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/tmmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/smmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/wmmintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avxintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avxvnniintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avxifmaintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avxvnniint8intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avxvnniint16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx2intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512fintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512cdintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512bwintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512dqintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vlbwintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vldqintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512ifmaintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512ifmavlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vbmiintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vbmivlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vpopcntdqintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vbmi2intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vbmi2vlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vnniintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vnnivlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vpopcntdqvlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512bitalgintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512bitalgvlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vp2intersectintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512vp2intersectvlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512fp16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512fp16vlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/shaintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/sm3intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/sha512intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/sm4intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/fmaintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/f16cintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/gfniintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/vaesintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/vpclmulqdqintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512bf16vlintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx512bf16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avxneconvertintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxtileintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxint8intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxbf16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxcomplexintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxavx512intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxtf32intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxtransposeintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxfp8intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/prfchwintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/keylockerintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxfp16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2mediaintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2-512mediaintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2convertintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2-512convertintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2bf16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2-512bf16intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2satcvtintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2-512satcvtintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2minmaxintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2-512minmaxintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/avx10_2copyintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/movrsintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/amxmovrsintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm3dnow.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/fma4intrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/ammintrin.h \
 D:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/xopintrin.h \
 D:/msys64/mingw64/include/pshpack4.h D:/msys64/mingw64/include/poppack.h \
 D:/msys64/mingw64/include/pshpack4.h \
 D:/msys64/mingw64/include/pshpack2.h D:/msys64/mingw64/include/poppack.h \
 D:/msys64/mingw64/include/pshpack2.h \
 D:/msys64/mingw64/include/pshpack8.h \
 D:/msys64/mingw64/include/pshpack8.h \
 D:/msys64/mingw64/include/ktmtypes.h D:/msys64/mingw64/include/winbase.h \
 D:/msys64/mingw64/include/apisetcconv.h \
 D:/msys64/mingw64/include/minwinbase.h \
 D:/msys64/mingw64/include/bemapiset.h \
 D:/msys64/mingw64/include/debugapi.h \
 D:/msys64/mingw64/include/errhandlingapi.h \
 D:/msys64/mingw64/include/fibersapi.h \
 D:/msys64/mingw64/include/fileapi.h \
 D:/msys64/mingw64/include/handleapi.h \
 D:/msys64/mingw64/include/heapapi.h D:/msys64/mingw64/include/ioapiset.h \
 D:/msys64/mingw64/include/interlockedapi.h \
 D:/msys64/mingw64/include/jobapi.h \
 D:/msys64/mingw64/include/libloaderapi.h \
 D:/msys64/mingw64/include/memoryapi.h \
 D:/msys64/mingw64/include/namedpipeapi.h \
 D:/msys64/mingw64/include/namespaceapi.h \
 D:/msys64/mingw64/include/processenv.h \
 D:/msys64/mingw64/include/processthreadsapi.h \
 D:/msys64/mingw64/include/processtopologyapi.h \
 D:/msys64/mingw64/include/profileapi.h \
 D:/msys64/mingw64/include/realtimeapiset.h \
 D:/msys64/mingw64/include/securityappcontainer.h \
 D:/msys64/mingw64/include/securitybaseapi.h \
 D:/msys64/mingw64/include/synchapi.h \
 D:/msys64/mingw64/include/sysinfoapi.h \
 D:/msys64/mingw64/include/systemtopologyapi.h \
 D:/msys64/mingw64/include/threadpoolapiset.h \
 D:/msys64/mingw64/include/threadpoollegacyapiset.h \
 D:/msys64/mingw64/include/utilapiset.h \
 D:/msys64/mingw64/include/wow64apiset.h \
 D:/msys64/mingw64/include/winerror.h \
 D:/msys64/mingw64/include/fltwinerror.h \
 D:/msys64/mingw64/include/timezoneapi.h \
 D:/msys64/mingw64/include/wingdi.h D:/msys64/mingw64/include/pshpack1.h \
 D:/msys64/mingw64/include/winuser.h D:/msys64/mingw64/include/tvout.h \
 D:/msys64/mingw64/include/winnls.h \
 D:/msys64/mingw64/include/datetimeapi.h \
 D:/msys64/mingw64/include/stringapiset.h \
 D:/msys64/mingw64/include/wincon.h \
 D:/msys64/mingw64/include/wincontypes.h \
 D:/msys64/mingw64/include/consoleapi.h \
 D:/msys64/mingw64/include/consoleapi2.h \
 D:/msys64/mingw64/include/consoleapi3.h \
 D:/msys64/mingw64/include/winver.h D:/msys64/mingw64/include/winreg.h \
 D:/msys64/mingw64/include/reason.h D:/msys64/mingw64/include/winnetwk.h \
 D:/msys64/mingw64/include/wnnc.h D:/msys64/mingw64/include/virtdisk.h \
 D:/msys64/mingw64/include/stralign.h \
 D:/msys64/mingw64/include/sec_api/stralign_s.h \
 D:/msys64/mingw64/include/winsvc.h D:/msys64/mingw64/include/mcx.h \
 D:/msys64/mingw64/include/imm.h D:/msys64/mingw64/include/iphlpapi.h \
 D:/msys64/mingw64/include/iprtrmib.h D:/msys64/mingw64/include/mprapi.h \
 D:/msys64/mingw64/include/lmcons.h D:/msys64/mingw64/include/ras.h \
 D:/msys64/mingw64/include/inaddr.h \
 D:/msys64/mingw64/include/_bsd_types.h \
 D:/msys64/mingw64/include/in6addr.h D:/msys64/mingw64/include/naptypes.h \
 D:/msys64/mingw64/include/rpc.h D:/msys64/mingw64/include/rpcdce.h \
 D:/msys64/mingw64/include/rpcdcep.h D:/msys64/mingw64/include/rpcnsi.h \
 D:/msys64/mingw64/include/rpcnterr.h \
 D:/msys64/mingw64/include/rpcasync.h D:/msys64/mingw64/include/rpcndr.h \
 D:/msys64/mingw64/include/rpcnsip.h D:/msys64/mingw64/include/rpcsal.h \
 D:/msys64/mingw64/include/ole2.h D:/msys64/mingw64/include/objbase.h \
 D:/msys64/mingw64/include/combaseapi.h \
 D:/msys64/mingw64/include/wtypesbase.h \
 D:/msys64/mingw64/include/unknwnbase.h \
 D:/msys64/mingw64/include/objidlbase.h D:/msys64/mingw64/include/cguid.h \
 D:/msys64/mingw64/include/objidl.h D:/msys64/mingw64/include/unknwn.h \
 D:/msys64/mingw64/include/wtypes.h D:/msys64/mingw64/include/urlmon.h \
 D:/msys64/mingw64/include/oleidl.h D:/msys64/mingw64/include/servprov.h \
 D:/msys64/mingw64/include/msxml.h D:/msys64/mingw64/include/oaidl.h \
 D:/msys64/mingw64/include/propidl.h D:/msys64/mingw64/include/oleauto.h \
 D:/msys64/mingw64/include/ocidl.h D:/msys64/mingw64/include/wincrypt.h \
 D:/msys64/mingw64/include/bcrypt.h D:/msys64/mingw64/include/ncrypt.h \
 D:/msys64/mingw64/include/dpapi.h D:/msys64/mingw64/include/ipmib.h \
 D:/msys64/mingw64/include/nldef.h D:/msys64/mingw64/include/ipifcons.h \
 D:/msys64/mingw64/include/udpmib.h D:/msys64/mingw64/include/tcpmib.h \
 D:/msys64/mingw64/include/ipexport.h D:/msys64/mingw64/include/iptypes.h \
 D:/msys64/mingw64/include/ifdef.h D:/msys64/mingw64/include/tcpestats.h \
 D:/msys64/mingw64/include/netioapi.h \
 D:/msys64/mingw64/include/winsock2.h \
 D:/msys64/mingw64/include/psdk_inc/_ws1_undef.h \
 D:/msys64/mingw64/include/psdk_inc/_socket_types.h \
 D:/msys64/mingw64/include/psdk_inc/_fd_types.h \
 D:/msys64/mingw64/include/psdk_inc/_ip_types.h \
 D:/msys64/mingw64/include/psdk_inc/_wsadata.h \
 D:/msys64/mingw64/include/ws2def.h \
 D:/msys64/mingw64/include/psdk_inc/_wsa_errnos.h \
 D:/msys64/mingw64/include/qos.h D:/msys64/mingw64/include/ws2tcpip.h \
 D:/msys64/mingw64/include/ws2ipdef.h \
 D:/msys64/mingw64/include/psdk_inc/_ip_mreq1.h \
 D:/msys64/mingw64/include/mstcpip.h \
 D:/msys64/mingw64/include/c++/15.1.0/iostream \
 D:/msys64/mingw64/include/c++/15.1.0/ostream \
 D:/msys64/mingw64/include/c++/15.1.0/bits/ostream.h \
 D:/msys64/mingw64/include/c++/15.1.0/ios \
 D:/msys64/mingw64/include/c++/15.1.0/bits/ios_base.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/locale_classes.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/locale_classes.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/system_error \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/error_constants.h \
 D:/msys64/mingw64/include/c++/15.1.0/stdexcept \
 D:/msys64/mingw64/include/c++/15.1.0/streambuf \
 D:/msys64/mingw64/include/c++/15.1.0/bits/streambuf.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/bits/basic_ios.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/locale_facets.h \
 D:/msys64/mingw64/include/c++/15.1.0/cwctype \
 D:/msys64/mingw64/include/wctype.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/ctype_base.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/streambuf_iterator.h \
 D:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32/bits/ctype_inline.h \
 D:/msys64/mingw64/include/c++/15.1.0/bits/locale_facets.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/bits/basic_ios.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/bits/ostream.tcc \
 D:/msys64/mingw64/include/c++/15.1.0/istream \
 D:/msys64/mingw64/include/c++/15.1.0/bits/istream.tcc
