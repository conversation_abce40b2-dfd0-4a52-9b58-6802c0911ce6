^E:\WORKSPACE\VSCODE\CHECKNETWORK\TESTS\CMAKELISTS.TXT
setlocal
D:\msys64\mingw64\bin\cmake.exe -SE:/workspace/vscode/CheckNetwork -BE:/workspace/vscode/CheckNetwork/build --check-stamp-file E:/workspace/vscode/CheckNetwork/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
