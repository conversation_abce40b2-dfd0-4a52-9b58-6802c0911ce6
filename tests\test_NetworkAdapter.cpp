#include <gtest/gtest.h>
#include "NetworkAdapter.h"
#include <thread>
#include <chrono>

class NetworkAdapterTest : public ::testing::Test {
protected:
    void SetUp() override {
        adapter = std::make_unique<NetworkAdapter>();
    }
    
    void TearDown() override {
        adapter.reset();
    }
    
    std::unique_ptr<NetworkAdapter> adapter;
};

// 测试IP地址验证功能
TEST_F(NetworkAdapterTest, ValidateIPAddress) {
    // 有效的IP地址
    EXPECT_TRUE(NetworkAdapter::isValidIPAddress("***********"));
    EXPECT_TRUE(NetworkAdapter::isValidIPAddress("********"));
    EXPECT_TRUE(NetworkAdapter::isValidIPAddress("**********"));
    EXPECT_TRUE(NetworkAdapter::isValidIPAddress("127.0.0.1"));
    EXPECT_TRUE(NetworkAdapter::isValidIPAddress("***************"));
    EXPECT_TRUE(NetworkAdapter::isValidIPAddress("0.0.0.0"));
    
    // 无效的IP地址
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress("256.1.1.1"));
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress("192.168.1"));
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress("***********.1"));
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress("abc.def.ghi.jkl"));
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress(""));
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress("192.168.-1.1"));
    EXPECT_FALSE(NetworkAdapter::isValidIPAddress("192.168.1.256"));
}

// 测试结果字符串转换
TEST_F(NetworkAdapterTest, ResultToString) {
    EXPECT_EQ(NetworkAdapter::resultToString(NetworkCheckResult::SUCCESS), 
              "Success: Network adapter found and connected");
    EXPECT_EQ(NetworkAdapter::resultToString(NetworkCheckResult::IP_NOT_FOUND), 
              "Error: No adapter found with the specified IP address");
    EXPECT_EQ(NetworkAdapter::resultToString(NetworkCheckResult::ADAPTER_DISCONNECTED), 
              "Error: Adapter found but not connected");
    EXPECT_EQ(NetworkAdapter::resultToString(NetworkCheckResult::ADAPTER_DISABLED), 
              "Error: Adapter found but disabled");
    EXPECT_EQ(NetworkAdapter::resultToString(NetworkCheckResult::SYSTEM_ERROR), 
              "Error: System error occurred");
    EXPECT_EQ(NetworkAdapter::resultToString(NetworkCheckResult::INVALID_IP), 
              "Error: Invalid IP address format");
}

// 测试获取所有适配器
TEST_F(NetworkAdapterTest, GetAllAdapters) {
    auto adapters = adapter->getAllAdapters();
    
    // 系统应该至少有一个网络适配器（回环接口通常被过滤掉）
    // 但在某些测试环境中可能没有，所以这里只检查函数不崩溃
    EXPECT_NO_THROW(adapter->getAllAdapters());
    
    // 如果有适配器，检查基本信息
    for (const auto& adapterInfo : adapters) {
        EXPECT_FALSE(adapterInfo.name.empty());
        EXPECT_FALSE(adapterInfo.ipAddress.empty());
        EXPECT_TRUE(NetworkAdapter::isValidIPAddress(adapterInfo.ipAddress));
    }
}

// 测试无效IP地址检查
TEST_F(NetworkAdapterTest, CheckInvalidIPAddress) {
    NetworkCheckResult result = adapter->checkNetworkAdapter("invalid.ip.address");
    EXPECT_EQ(result, NetworkCheckResult::INVALID_IP);
    EXPECT_FALSE(adapter->getLastError().empty());
}

// 测试不存在的IP地址
TEST_F(NetworkAdapterTest, CheckNonExistentIP) {
    // 使用一个不太可能存在的IP地址
    NetworkCheckResult result = adapter->checkNetworkAdapter("***************");
    
    // 结果应该是IP_NOT_FOUND（除非系统真的有这个IP）
    EXPECT_TRUE(result == NetworkCheckResult::IP_NOT_FOUND || 
                result == NetworkCheckResult::SUCCESS ||
                result == NetworkCheckResult::ADAPTER_DISCONNECTED ||
                result == NetworkCheckResult::ADAPTER_DISABLED);
}

// 测试查找适配器功能
TEST_F(NetworkAdapterTest, FindAdapterByIP) {
    // 先获取所有适配器
    auto adapters = adapter->getAllAdapters();
    
    if (!adapters.empty()) {
        // 使用第一个适配器的IP进行查找测试
        std::string testIP = adapters[0].ipAddress;
        AdapterInfo foundAdapter;
        
        bool found = adapter->findAdapterByIP(testIP, foundAdapter);
        EXPECT_TRUE(found);
        EXPECT_EQ(foundAdapter.ipAddress, testIP);
        EXPECT_FALSE(foundAdapter.name.empty());
    }
    
    // 测试查找不存在的IP
    AdapterInfo notFoundAdapter;
    bool found = adapter->findAdapterByIP("***************", notFoundAdapter);
    // 通常应该找不到，但如果系统真的有这个IP，也是正常的
    if (!found) {
        EXPECT_FALSE(adapter->getLastError().empty());
    }
}

// 测试错误处理
TEST_F(NetworkAdapterTest, ErrorHandling) {
    // 测试无效IP的错误信息
    adapter->checkNetworkAdapter("invalid");
    EXPECT_FALSE(adapter->getLastError().empty());
    
    // 测试查找无效IP的错误信息
    AdapterInfo info;
    adapter->findAdapterByIP("invalid", info);
    EXPECT_FALSE(adapter->getLastError().empty());
}

// 性能测试
TEST_F(NetworkAdapterTest, PerformanceTest) {
    auto start = std::chrono::high_resolution_clock::now();
    
    // 执行多次操作
    for (int i = 0; i < 10; ++i) {
        adapter->getAllAdapters();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // 10次操作应该在合理时间内完成（比如5秒）
    EXPECT_LT(duration.count(), 5000);
}
