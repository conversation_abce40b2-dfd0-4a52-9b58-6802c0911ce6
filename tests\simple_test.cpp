#include "NetworkAdapter.h"
#include <iostream>
#include <cassert>
#include <vector>

// Simple test framework
class SimpleTest {
private:
    static int totalTests;
    static int passedTests;
    static int failedTests;

public:
    static void assertTrue(bool condition, const std::string& testName) {
        totalTests++;
        if (condition) {
            passedTests++;
            std::cout << "[PASS] " << testName << std::endl;
        } else {
            failedTests++;
            std::cout << "[FAIL] " << testName << std::endl;
        }
    }

    static void assertFalse(bool condition, const std::string& testName) {
        assertTrue(!condition, testName);
    }

    static void assertEqual(const std::string& expected, const std::string& actual, const std::string& testName) {
        assertTrue(expected == actual, testName + " (expected: '" + expected + "', actual: '" + actual + "')");
    }

    static void printResults() {
        std::cout << "\n=== Test Results ===" << std::endl;
        std::cout << "Total tests: " << totalTests << std::endl;
        std::cout << "Passed: " << passedTests << std::endl;
        std::cout << "Failed: " << failedTests << std::endl;
        std::cout << "Success rate: " << (totalTests > 0 ? (passedTests * 100 / totalTests) : 0) << "%" << std::endl;
    }

    static bool allTestsPassed() {
        return failedTests == 0;
    }
};

int SimpleTest::totalTests = 0;
int SimpleTest::passedTests = 0;
int SimpleTest::failedTests = 0;

void testIPValidation() {
    std::cout << "\n=== Testing IP Address Validation ===" << std::endl;
    
    // Valid IP addresses
    SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress("***********"), "Valid IP: ***********");
    SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress("********"), "Valid IP: ********");
    SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress("**********"), "Valid IP: **********");
    SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress("127.0.0.1"), "Valid IP: 127.0.0.1");
    SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress("***************"), "Valid IP: ***************");
    SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress("0.0.0.0"), "Valid IP: 0.0.0.0");
    
    // Invalid IP addresses
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress("256.1.1.1"), "Invalid IP: 256.1.1.1");
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress("192.168.1"), "Invalid IP: 192.168.1");
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress("***********.1"), "Invalid IP: ***********.1");
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress("abc.def.ghi.jkl"), "Invalid IP: abc.def.ghi.jkl");
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress(""), "Invalid IP: empty string");
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress("192.168.-1.1"), "Invalid IP: 192.168.-1.1");
    SimpleTest::assertFalse(NetworkAdapter::isValidIPAddress("192.168.1.256"), "Invalid IP: 192.168.1.256");
}

void testResultToString() {
    std::cout << "\n=== Testing Result to String Conversion ===" << std::endl;
    
    SimpleTest::assertEqual("Success: Network adapter found and connected", 
                           NetworkAdapter::resultToString(NetworkCheckResult::SUCCESS), 
                           "SUCCESS result string");
    
    SimpleTest::assertEqual("Error: No adapter found with the specified IP address", 
                           NetworkAdapter::resultToString(NetworkCheckResult::IP_NOT_FOUND), 
                           "IP_NOT_FOUND result string");
    
    SimpleTest::assertEqual("Error: Adapter found but not connected", 
                           NetworkAdapter::resultToString(NetworkCheckResult::ADAPTER_DISCONNECTED), 
                           "ADAPTER_DISCONNECTED result string");
    
    SimpleTest::assertEqual("Error: Adapter found but disabled", 
                           NetworkAdapter::resultToString(NetworkCheckResult::ADAPTER_DISABLED), 
                           "ADAPTER_DISABLED result string");
    
    SimpleTest::assertEqual("Error: System error occurred", 
                           NetworkAdapter::resultToString(NetworkCheckResult::SYSTEM_ERROR), 
                           "SYSTEM_ERROR result string");
    
    SimpleTest::assertEqual("Error: Invalid IP address format", 
                           NetworkAdapter::resultToString(NetworkCheckResult::INVALID_IP), 
                           "INVALID_IP result string");
}

void testNetworkAdapter() {
    std::cout << "\n=== Testing NetworkAdapter Functionality ===" << std::endl;
    
    NetworkAdapter adapter;
    
    // Test invalid IP check
    NetworkCheckResult result = adapter.checkNetworkAdapter("invalid.ip.address");
    SimpleTest::assertTrue(result == NetworkCheckResult::INVALID_IP, "Invalid IP returns INVALID_IP");
    SimpleTest::assertFalse(adapter.getLastError().empty(), "Error message set for invalid IP");
    
    // Test getting all adapters
    auto adapters = adapter.getAllAdapters();
    std::cout << "Found " << adapters.size() << " network adapters" << std::endl;
    
    // Basic validation of adapter info
    for (const auto& adapterInfo : adapters) {
        SimpleTest::assertFalse(adapterInfo.name.empty(), "Adapter name not empty");
        SimpleTest::assertFalse(adapterInfo.ipAddress.empty(), "Adapter IP not empty");
        SimpleTest::assertTrue(NetworkAdapter::isValidIPAddress(adapterInfo.ipAddress), 
                              "Adapter IP is valid: " + adapterInfo.ipAddress);
    }
    
    // Test with a non-existent IP
    result = adapter.checkNetworkAdapter("***************");
    SimpleTest::assertTrue(result == NetworkCheckResult::IP_NOT_FOUND || 
                          result == NetworkCheckResult::SUCCESS ||
                          result == NetworkCheckResult::ADAPTER_DISCONNECTED ||
                          result == NetworkCheckResult::ADAPTER_DISABLED, 
                          "Non-existent IP returns appropriate result");
    
    // Test findAdapterByIP with existing adapter (if any)
    if (!adapters.empty()) {
        std::string testIP = adapters[0].ipAddress;
        AdapterInfo foundAdapter;
        bool found = adapter.findAdapterByIP(testIP, foundAdapter);
        SimpleTest::assertTrue(found, "Find existing adapter by IP");
        if (found) {
            SimpleTest::assertEqual(testIP, foundAdapter.ipAddress, "Found adapter has correct IP");
            SimpleTest::assertFalse(foundAdapter.name.empty(), "Found adapter has name");
        }
    }
    
    // Test findAdapterByIP with non-existent IP
    AdapterInfo notFoundAdapter;
    bool found = adapter.findAdapterByIP("***************", notFoundAdapter);
    if (!found) {
        SimpleTest::assertFalse(adapter.getLastError().empty(), "Error message set for not found adapter");
    }
}

int main() {
    std::cout << "=== CheckNetwork Simple Test Suite ===" << std::endl;
    
    try {
        testIPValidation();
        testResultToString();
        testNetworkAdapter();
        
        SimpleTest::printResults();
        
        if (SimpleTest::allTestsPassed()) {
            std::cout << "\nAll tests passed!" << std::endl;
            return 0;
        } else {
            std::cout << "\nSome tests failed!" << std::endl;
            return 1;
        }
    } catch (const std::exception& e) {
        std::cout << "Test execution failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
