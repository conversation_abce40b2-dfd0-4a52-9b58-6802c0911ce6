#include "NetworkAdapter.h"
#include <iostream>
#include <iomanip>

void printAdapterInfo(const AdapterInfo& adapter) {
    std::cout << "  Name: " << adapter.name << std::endl;
    std::cout << "  Description: " << adapter.description << std::endl;
    std::cout << "  IP Address: " << adapter.ipAddress << std::endl;
    std::cout << "  Subnet Mask: " << adapter.subnetMask << std::endl;
    std::cout << "  Enabled: " << (adapter.isEnabled ? "Yes" : "No") << std::endl;
    std::cout << "  Connected: " << (adapter.isConnected ? "Yes" : "No") << std::endl;
}

void showUsage(const char* programName) {
    std::cout << "用法: " << programName << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  -h, --help           显示此帮助信息" << std::endl;
    std::cout << "  -l, --list           列出所有网络适配器" << std::endl;
    std::cout << "  -c, --check <IP>     检查指定 IP 的适配器是否已连接" << std::endl;
    std::cout << "  -f, --find <IP>      根据 IP 地址查找适配器信息" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << programName << " --list" << std::endl;
    std::cout << "  " << programName << " --check *************" << std::endl;
    std::cout << "  " << programName << " --find *********" << std::endl;
}

int main(int argc, char* argv[]) {
    NetworkAdapter adapter;

    if (argc < 2) {
        showUsage(argv[0]);
        return 1;
    }

    std::string option = argv[1];

    if (option == "-h" || option == "--help") {
        showUsage(argv[0]);
        return 0;
    }
    else if (option == "-l" || option == "--list") {
        std::cout << "=== 网络适配器列表 ===" << std::endl;

        auto adapters = adapter.getAllAdapters();
        if (adapters.empty()) {
            std::cout << "未找到网络适配器。" << std::endl;
            if (!adapter.getLastError().empty()) {
                std::cout << "错误: " << adapter.getLastError() << std::endl;
            }
            return 1;
        }

        for (size_t i = 0; i < adapters.size(); ++i) {
            std::cout << "适配器 " << (i + 1) << ":" << std::endl;
            printAdapterInfo(adapters[i]);
            std::cout << std::endl;
        }
    }
    else if ((option == "-c" || option == "--check") && argc >= 3) {
        std::string ipAddress = argv[2];
        std::cout << "=== 检查网络适配器 ===" << std::endl;
        std::cout << "IP 地址: " << ipAddress << std::endl;

        NetworkCheckResult result = adapter.checkNetworkAdapter(ipAddress);
        std::cout << "结果: " << NetworkAdapter::resultToString(result) << std::endl;

        if (result != NetworkCheckResult::SUCCESS && !adapter.getLastError().empty()) {
            std::cout << "详情: " << adapter.getLastError() << std::endl;
        }

        return (result == NetworkCheckResult::SUCCESS) ? 0 : 1;
    }
    else if ((option == "-f" || option == "--find") && argc >= 3) {
        std::string ipAddress = argv[2];
        std::cout << "=== 根据 IP 查找适配器 ===" << std::endl;
        std::cout << "IP 地址: " << ipAddress << std::endl;

        AdapterInfo adapterInfo;
        if (adapter.findAdapterByIP(ipAddress, adapterInfo)) {
            std::cout << "找到适配器:" << std::endl;
            printAdapterInfo(adapterInfo);
        } else {
            std::cout << "未找到适配器。" << std::endl;
            if (!adapter.getLastError().empty()) {
                std::cout << "错误: " << adapter.getLastError() << std::endl;
            }
            return 1;
        }
    }
    else {
        std::cout << "无效选项或缺少参数。" << std::endl;
        showUsage(argv[0]);
        return 1;
    }

    return 0;
}
