#include "NetworkAdapter.h"
#include <iostream>
#include <iomanip>

void printAdapterInfo(const AdapterInfo& adapter) {
    std::cout << "  Name: " << adapter.name << std::endl;
    std::cout << "  Description: " << adapter.description << std::endl;
    std::cout << "  IP Address: " << adapter.ipAddress << std::endl;
    std::cout << "  Subnet Mask: " << adapter.subnetMask << std::endl;
    std::cout << "  Enabled: " << (adapter.isEnabled ? "Yes" : "No") << std::endl;
    std::cout << "  Connected: " << (adapter.isConnected ? "Yes" : "No") << std::endl;
}

void showUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -h, --help           Show this help message" << std::endl;
    std::cout << "  -l, --list           List all network adapters" << std::endl;
    std::cout << "  -c, --check <IP>     Check if adapter with specified IP is connected" << std::endl;
    std::cout << "  -f, --find <IP>      Find adapter information by IP address" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  " << programName << " --list" << std::endl;
    std::cout << "  " << programName << " --check *************" << std::endl;
    std::cout << "  " << programName << " --find *********" << std::endl;
}

int main(int argc, char* argv[]) {
    NetworkAdapter adapter;

    if (argc < 2) {
        showUsage(argv[0]);
        return 1;
    }

    std::string option = argv[1];

    if (option == "-h" || option == "--help") {
        showUsage(argv[0]);
        return 0;
    }
    else if (option == "-l" || option == "--list") {
        std::cout << "=== Network Adapters List ===" << std::endl;

        auto adapters = adapter.getAllAdapters();
        if (adapters.empty()) {
            std::cout << "No network adapters found." << std::endl;
            if (!adapter.getLastError().empty()) {
                std::cout << "Error: " << adapter.getLastError() << std::endl;
            }
            return 1;
        }

        for (size_t i = 0; i < adapters.size(); ++i) {
            std::cout << "Adapter " << (i + 1) << ":" << std::endl;
            printAdapterInfo(adapters[i]);
            std::cout << std::endl;
        }
    }
    else if ((option == "-c" || option == "--check") && argc >= 3) {
        std::string ipAddress = argv[2];
        std::cout << "=== Checking Network Adapter ===" << std::endl;
        std::cout << "IP Address: " << ipAddress << std::endl;

        NetworkCheckResult result = adapter.checkNetworkAdapter(ipAddress);
        std::cout << "Result: " << NetworkAdapter::resultToString(result) << std::endl;

        if (result != NetworkCheckResult::SUCCESS && !adapter.getLastError().empty()) {
            std::cout << "Details: " << adapter.getLastError() << std::endl;
        }

        return (result == NetworkCheckResult::SUCCESS) ? 0 : 1;
    }
    else if ((option == "-f" || option == "--find") && argc >= 3) {
        std::string ipAddress = argv[2];
        std::cout << "=== Finding Adapter by IP ===" << std::endl;
        std::cout << "IP Address: " << ipAddress << std::endl;

        AdapterInfo adapterInfo;
        if (adapter.findAdapterByIP(ipAddress, adapterInfo)) {
            std::cout << "Adapter found:" << std::endl;
            printAdapterInfo(adapterInfo);
        } else {
            std::cout << "Adapter not found." << std::endl;
            if (!adapter.getLastError().empty()) {
                std::cout << "Error: " << adapter.getLastError() << std::endl;
            }
            return 1;
        }
    }
    else {
        std::cout << "Invalid option or missing arguments." << std::endl;
        showUsage(argv[0]);
        return 1;
    }

    return 0;
}
