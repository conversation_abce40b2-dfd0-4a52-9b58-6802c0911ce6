#include <gtest/gtest.h>
#include <iostream>

// 自定义测试监听器，用于输出更详细的测试信息
class DetailedTestListener : public ::testing::EmptyTestEventListener {
public:
    void OnTestStart(const ::testing::TestInfo& test_info) override {
        std::cout << "[ RUNNING ] " << test_info.test_case_name() 
                  << "." << test_info.name() << std::endl;
    }
    
    void OnTestEnd(const ::testing::TestInfo& test_info) override {
        if (test_info.result()->Passed()) {
            std::cout << "[ PASSED  ] " << test_info.test_case_name() 
                      << "." << test_info.name() << std::endl;
        } else {
            std::cout << "[ FAILED  ] " << test_info.test_case_name() 
                      << "." << test_info.name() << std::endl;
        }
    }
    
    void OnTestProgramStart(const ::testing::UnitTest& unit_test) override {
        std::cout << "=== CheckNetwork Unit Tests ===" << std::endl;
        std::cout << "Running " << unit_test.total_test_count() << " tests." << std::endl;
    }
    
    void OnTestProgramEnd(const ::testing::UnitTest& unit_test) override {
        std::cout << "=== Test Results ===" << std::endl;
        std::cout << "Tests run: " << unit_test.total_test_count() << std::endl;
        std::cout << "Passed: " << unit_test.successful_test_count() << std::endl;
        std::cout << "Failed: " << unit_test.failed_test_count() << std::endl;
        
        if (unit_test.failed_test_count() > 0) {
            std::cout << "Some tests failed. Please check the output above." << std::endl;
        } else {
            std::cout << "All tests passed!" << std::endl;
        }
    }
};

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    
    // 添加自定义监听器
    ::testing::TestEventListeners& listeners = ::testing::UnitTest::GetInstance()->listeners();
    listeners.Append(new DetailedTestListener);
    
    return RUN_ALL_TESTS();
}
