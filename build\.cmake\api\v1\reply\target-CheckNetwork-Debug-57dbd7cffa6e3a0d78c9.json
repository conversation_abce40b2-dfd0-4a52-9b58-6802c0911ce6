{"archive": {}, "artifacts": [{"path": "libCheckNetwork.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_compile_options", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 42, "parent": 0}, {"command": 1, "file": 0, "line": 62, "parent": 0}, {"command": 2, "file": 0, "line": 15, "parent": 0}, {"command": 3, "file": 0, "line": 23, "parent": 0}, {"command": 4, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-pedantic"}], "defines": [{"backtrace": 4, "define": "WIN32_LEAN_AND_MEAN"}], "includes": [{"backtrace": 5, "path": "E:/workspace/vscode/CheckNetwork/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1]}], "id": "CheckNetwork::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/CheckNetwork"}}, "name": "CheckNetwork", "nameOnDisk": "libCheckNetwork.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/NetworkAdapter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/NetworkAdapter_Windows.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}