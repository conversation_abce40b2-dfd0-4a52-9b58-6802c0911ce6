#include "NetworkAdapter.h"
#include <regex>
#include <sstream>
#include <algorithm>

// PIMPL implementation class declaration
class NetworkAdapter::Impl {
public:
    std::string lastError;

    void setError(const std::string& error) {
        lastError = error;
    }

    void clearError() {
        lastError.clear();
    }
};

NetworkAdapter::NetworkAdapter() : pImpl(std::make_unique<Impl>()) {
}

NetworkAdapter::~NetworkAdapter() = default;

NetworkCheckResult NetworkAdapter::checkNetworkAdapter(const std::string& ipAddress) {
    pImpl->clearError();

    // Validate IP address format
    if (!isValidIPAddress(ipAddress)) {
        pImpl->setError("Invalid IP address format: " + ipAddress);
        return NetworkCheckResult::INVALID_IP;
    }

    try {
        // Get all adapter information
        auto adapters = getAdaptersImpl();

        // Find adapter containing specified IP
        for (const auto& adapter : adapters) {
            if (adapter.ipAddress == ipAddress) {
                // Check if adapter is enabled
                if (!adapter.isEnabled) {
                    pImpl->setError("Adapter with IP " + ipAddress + " is disabled");
                    return NetworkCheckResult::ADAPTER_DISABLED;
                }

                // Check adapter connection status
                if (!adapter.isConnected) {
                    pImpl->setError("Adapter with IP " + ipAddress + " is not connected");
                    return NetworkCheckResult::ADAPTER_DISCONNECTED;
                }

                // Found and connected normally
                return NetworkCheckResult::SUCCESS;
            }
        }

        // Specified IP not found
        pImpl->setError("No adapter found with IP address: " + ipAddress);
        return NetworkCheckResult::IP_NOT_FOUND;

    } catch (const std::exception& e) {
        pImpl->setError("System error: " + std::string(e.what()));
        return NetworkCheckResult::SYSTEM_ERROR;
    }
}

std::vector<AdapterInfo> NetworkAdapter::getAllAdapters() {
    pImpl->clearError();
    
    try {
        return getAdaptersImpl();
    } catch (const std::exception& e) {
        pImpl->setError("Failed to get adapters: " + std::string(e.what()));
        return {};
    }
}

bool NetworkAdapter::findAdapterByIP(const std::string& ipAddress, AdapterInfo& adapterInfo) {
    pImpl->clearError();
    
    if (!isValidIPAddress(ipAddress)) {
        pImpl->setError("Invalid IP address format: " + ipAddress);
        return false;
    }
    
    try {
        auto adapters = getAdaptersImpl();
        
        for (const auto& adapter : adapters) {
            if (adapter.ipAddress == ipAddress) {
                adapterInfo = adapter;
                return true;
            }
        }
        
        pImpl->setError("No adapter found with IP address: " + ipAddress);
        return false;
        
    } catch (const std::exception& e) {
        pImpl->setError("System error: " + std::string(e.what()));
        return false;
    }
}

std::string NetworkAdapter::getLastError() const {
    return pImpl->lastError;
}

std::string NetworkAdapter::resultToString(NetworkCheckResult result) {
    switch (result) {
        case NetworkCheckResult::SUCCESS:
            return "Success: Network adapter found and connected";
        case NetworkCheckResult::IP_NOT_FOUND:
            return "Error: No adapter found with the specified IP address";
        case NetworkCheckResult::ADAPTER_DISCONNECTED:
            return "Error: Adapter found but not connected";
        case NetworkCheckResult::ADAPTER_DISABLED:
            return "Error: Adapter found but disabled";
        case NetworkCheckResult::SYSTEM_ERROR:
            return "Error: System error occurred";
        case NetworkCheckResult::INVALID_IP:
            return "Error: Invalid IP address format";
        default:
            return "Unknown result";
    }
}

bool NetworkAdapter::isValidIPAddress(const std::string& ipAddress) {
    // IPv4 address regular expression
    std::regex ipv4Regex(
        R"(^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)"
    );

    return std::regex_match(ipAddress, ipv4Regex);
}
