#include "NetworkAdapter.h"
#include <regex>
#include <sstream>
#include <algorithm>

// PIMPL 实现类声明
class NetworkAdapter::Impl {
public:
    std::string lastError;

    void setError(const std::string& error) {
        lastError = error;
    }

    void clearError() {
        lastError.clear();
    }
};

NetworkAdapter::NetworkAdapter() : pImpl(std::make_unique<Impl>()) {
}

NetworkAdapter::~NetworkAdapter() = default;

NetworkCheckResult NetworkAdapter::checkNetworkAdapter(const std::string& ipAddress) {
    pImpl->clearError();

    // 验证 IP 地址格式
    if (!isValidIPAddress(ipAddress)) {
        pImpl->setError("IP 地址格式无效: " + ipAddress);
        return NetworkCheckResult::INVALID_IP;
    }

    try {
        // 获取所有适配器信息
        auto adapters = getAdaptersImpl();

        // 查找包含指定 IP 的适配器
        for (const auto& adapter : adapters) {
            if (adapter.ipAddress == ipAddress) {
                // 检查适配器是否已启用
                if (!adapter.isEnabled) {
                    pImpl->setError("IP 为 " + ipAddress + " 的适配器已禁用");
                    return NetworkCheckResult::ADAPTER_DISABLED;
                }

                // 检查适配器连接状态
                if (!adapter.isConnected) {
                    pImpl->setError("IP 为 " + ipAddress + " 的适配器未连接");
                    return NetworkCheckResult::ADAPTER_DISCONNECTED;
                }

                // 正常找到并已连接
                return NetworkCheckResult::SUCCESS;
            }
        }

        // 未找到指定的 IP
        pImpl->setError("未找到 IP 地址为 " + ipAddress + " 的适配器");
        return NetworkCheckResult::IP_NOT_FOUND;

    } catch (const std::exception& e) {
        pImpl->setError("系统错误: " + std::string(e.what()));
        return NetworkCheckResult::SYSTEM_ERROR;
    }
}

std::vector<AdapterInfo> NetworkAdapter::getAllAdapters() {
    pImpl->clearError();
    
    try {
        return getAdaptersImpl();
    } catch (const std::exception& e) {
        pImpl->setError("获取适配器失败: " + std::string(e.what()));
        return {};
    }
}

bool NetworkAdapter::findAdapterByIP(const std::string& ipAddress, AdapterInfo& adapterInfo) {
    pImpl->clearError();
    
    if (!isValidIPAddress(ipAddress)) {
        pImpl->setError("IP 地址格式无效: " + ipAddress);
        return false;
    }
    
    try {
        auto adapters = getAdaptersImpl();
        
        for (const auto& adapter : adapters) {
            if (adapter.ipAddress == ipAddress) {
                adapterInfo = adapter;
                return true;
            }
        }
        
        pImpl->setError("未找到 IP 地址为 " + ipAddress + " 的适配器");
        return false;
        
    } catch (const std::exception& e) {
        pImpl->setError("系统错误: " + std::string(e.what()));
        return false;
    }
}

std::string NetworkAdapter::getLastError() const {
    return pImpl->lastError;
}

std::string NetworkAdapter::resultToString(NetworkCheckResult result) {
    switch (result) {
        case NetworkCheckResult::SUCCESS:
            return "成功：找到网络适配器并已连接";
        case NetworkCheckResult::IP_NOT_FOUND:
            return "错误：未找到具有指定 IP 地址的适配器";
        case NetworkCheckResult::ADAPTER_DISCONNECTED:
            return "错误：找到适配器但未连接";
        case NetworkCheckResult::ADAPTER_DISABLED:
            return "错误：找到适配器但已禁用";
        case NetworkCheckResult::SYSTEM_ERROR:
            return "错误：发生系统错误";
        case NetworkCheckResult::INVALID_IP:
            return "错误：IP 地址格式无效";
        default:
            return "未知结果";
    }
}

bool NetworkAdapter::isValidIPAddress(const std::string& ipAddress) {
    // IPv4 地址正则表达式
    std::regex ipv4Regex(
        R"(^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)"
    );

    return std::regex_match(ipAddress, ipv4Regex);
}
