{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/4.0.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/FindGTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/GoogleTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestConfigVersion.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestTargets.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestTargets-release.cmake"}, {"path": "tests/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/FindGTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/GoogleTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestConfigVersion.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/msys64/mingw64/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "D:/msys64/mingw64/lib/cmake/GTest/GTestTargets.cmake"}], "kind": "cmakeFiles", "paths": {"build": "E:/workspace/vscode/CheckNetwork/build", "source": "E:/workspace/vscode/CheckNetwork"}, "version": {"major": 1, "minor": 1}}