# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/CheckNetwork.dir/all
all: CMakeFiles/CheckNetworkDemo.dir/all
all: tests/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/CheckNetwork.dir/codegen
codegen: CMakeFiles/CheckNetworkDemo.dir/codegen
codegen: tests/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: tests/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/CheckNetwork.dir/clean
clean: CMakeFiles/CheckNetworkDemo.dir/clean
clean: tests/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory tests

# Recursive "all" directory target.
tests/all: tests/CMakeFiles/SimpleNetworkTest.dir/all
tests/all: tests/CMakeFiles/CheckNetworkTests.dir/all
.PHONY : tests/all

# Recursive "codegen" directory target.
tests/codegen: tests/CMakeFiles/SimpleNetworkTest.dir/codegen
tests/codegen: tests/CMakeFiles/CheckNetworkTests.dir/codegen
.PHONY : tests/codegen

# Recursive "preinstall" directory target.
tests/preinstall:
.PHONY : tests/preinstall

# Recursive "clean" directory target.
tests/clean: tests/CMakeFiles/SimpleNetworkTest.dir/clean
tests/clean: tests/CMakeFiles/CheckNetworkTests.dir/clean
.PHONY : tests/clean

#=============================================================================
# Target rules for target CMakeFiles/CheckNetwork.dir

# All Build rule for target.
CMakeFiles/CheckNetwork.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=1,2,3 "Built target CheckNetwork"
.PHONY : CMakeFiles/CheckNetwork.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CheckNetwork.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CheckNetwork.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 0
.PHONY : CMakeFiles/CheckNetwork.dir/rule

# Convenience name for target.
CheckNetwork: CMakeFiles/CheckNetwork.dir/rule
.PHONY : CheckNetwork

# codegen rule for target.
CMakeFiles/CheckNetwork.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=1,2,3 "Finished codegen for target CheckNetwork"
.PHONY : CMakeFiles/CheckNetwork.dir/codegen

# clean rule for target.
CMakeFiles/CheckNetwork.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/clean
.PHONY : CMakeFiles/CheckNetwork.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/CheckNetworkDemo.dir

# All Build rule for target.
CMakeFiles/CheckNetworkDemo.dir/all: CMakeFiles/CheckNetwork.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=4,5 "Built target CheckNetworkDemo"
.PHONY : CMakeFiles/CheckNetworkDemo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/CheckNetworkDemo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/CheckNetworkDemo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 0
.PHONY : CMakeFiles/CheckNetworkDemo.dir/rule

# Convenience name for target.
CheckNetworkDemo: CMakeFiles/CheckNetworkDemo.dir/rule
.PHONY : CheckNetworkDemo

# codegen rule for target.
CMakeFiles/CheckNetworkDemo.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=4,5 "Finished codegen for target CheckNetworkDemo"
.PHONY : CMakeFiles/CheckNetworkDemo.dir/codegen

# clean rule for target.
CMakeFiles/CheckNetworkDemo.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/clean
.PHONY : CMakeFiles/CheckNetworkDemo.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/SimpleNetworkTest.dir

# All Build rule for target.
tests/CMakeFiles/SimpleNetworkTest.dir/all: CMakeFiles/CheckNetwork.dir/all
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/depend
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=9,10 "Built target SimpleNetworkTest"
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/SimpleNetworkTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/CMakeFiles/SimpleNetworkTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 0
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/rule

# Convenience name for target.
SimpleNetworkTest: tests/CMakeFiles/SimpleNetworkTest.dir/rule
.PHONY : SimpleNetworkTest

# codegen rule for target.
tests/CMakeFiles/SimpleNetworkTest.dir/codegen:
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=9,10 "Finished codegen for target SimpleNetworkTest"
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/codegen

# clean rule for target.
tests/CMakeFiles/SimpleNetworkTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/clean
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/clean

#=============================================================================
# Target rules for target tests/CMakeFiles/CheckNetworkTests.dir

# All Build rule for target.
tests/CMakeFiles/CheckNetworkTests.dir/all: CMakeFiles/CheckNetwork.dir/all
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/depend
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=6,7,8 "Built target CheckNetworkTests"
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/all

# Build rule for subdir invocation for target.
tests/CMakeFiles/CheckNetworkTests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/CMakeFiles/CheckNetworkTests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 0
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/rule

# Convenience name for target.
CheckNetworkTests: tests/CMakeFiles/CheckNetworkTests.dir/rule
.PHONY : CheckNetworkTests

# codegen rule for target.
tests/CMakeFiles/CheckNetworkTests.dir/codegen:
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=6,7,8 "Finished codegen for target CheckNetworkTests"
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/codegen

# clean rule for target.
tests/CMakeFiles/CheckNetworkTests.dir/clean:
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/clean
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

