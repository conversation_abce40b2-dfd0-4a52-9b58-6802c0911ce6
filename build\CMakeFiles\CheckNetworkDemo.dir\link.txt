D:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\CheckNetworkDemo.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\CheckNetworkDemo.dir/objects.a @CMakeFiles\CheckNetworkDemo.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\CheckNetworkDemo.dir/objects.a -Wl,--no-whole-archive -o CheckNetworkDemo.exe -Wl,--out-implib,libCheckNetworkDemo.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\CheckNetworkDemo.dir\linkLibs.rsp
