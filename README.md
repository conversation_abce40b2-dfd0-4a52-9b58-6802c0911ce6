# CheckNetwork - 跨平台网络适配器检测库

## 项目简介

CheckNetwork 是一个跨平台的C++库，用于检测系统网络适配器列表中是否包含指定的IP地址，并判断该网络适配器的网线连接状态。该库支持Windows和Linux平台，提供统一的API接口。

## 功能特性

- ✅ 跨平台支持（Windows和Linux）
- ✅ 检测指定IP地址的网络适配器是否存在
- ✅ 判断网络适配器的连接状态（已连接/已断开）
- ✅ 获取所有网络适配器信息
- ✅ 完善的错误处理机制
- ✅ 单元测试覆盖
- ✅ 清晰的API设计

## 系统要求

### Windows
- Windows 7 或更高版本
- Visual Studio 2017 或更高版本（支持C++17）
- CMake 3.16 或更高版本

### Linux
- Linux内核 2.6 或更高版本
- GCC 7.0 或更高版本（支持C++17）
- CMake 3.16 或更高版本

## 构建说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd CheckNetwork
```

### 2. 创建构建目录
```bash
mkdir build
cd build
```

### 3. 配置和构建

#### Windows (使用Visual Studio)
```cmd
cmake .. -G "Visual Studio 16 2019"
cmake --build . --config Release
```

#### Linux
```bash
cmake ..
make -j$(nproc)
```

### 4. 运行测试（可选）
```bash
# 如果安装了Google Test
ctest --verbose
```

## 使用方法

### 基本用法

```cpp
#include "NetworkAdapter.h"
#include <iostream>

int main() {
    NetworkAdapter adapter;
    
    // 检查指定IP的适配器是否连接
    NetworkCheckResult result = adapter.checkNetworkAdapter("*************");
    
    if (result == NetworkCheckResult::SUCCESS) {
        std::cout << "网络适配器已连接！" << std::endl;
    } else {
        std::cout << "检查失败: " << NetworkAdapter::resultToString(result) << std::endl;
        std::cout << "错误详情: " << adapter.getLastError() << std::endl;
    }
    
    return 0;
}
```

### 获取所有适配器信息

```cpp
NetworkAdapter adapter;
auto adapters = adapter.getAllAdapters();

for (const auto& info : adapters) {
    std::cout << "适配器: " << info.name << std::endl;
    std::cout << "IP地址: " << info.ipAddress << std::endl;
    std::cout << "连接状态: " << (info.isConnected ? "已连接" : "未连接") << std::endl;
    std::cout << "启用状态: " << (info.isEnabled ? "已启用" : "已禁用") << std::endl;
    std::cout << "---" << std::endl;
}
```

### 查找特定IP的适配器

```cpp
NetworkAdapter adapter;
AdapterInfo adapterInfo;

if (adapter.findAdapterByIP("*********", adapterInfo)) {
    std::cout << "找到适配器: " << adapterInfo.name << std::endl;
    std::cout << "描述: " << adapterInfo.description << std::endl;
    std::cout << "子网掩码: " << adapterInfo.subnetMask << std::endl;
} else {
    std::cout << "未找到指定IP的适配器" << std::endl;
}
```

## API 参考

### NetworkAdapter 类

#### 主要方法

- `NetworkCheckResult checkNetworkAdapter(const std::string& ipAddress)`
  - 检查指定IP地址的网络适配器是否存在且连接正常
  - 返回检查结果枚举值

- `std::vector<AdapterInfo> getAllAdapters()`
  - 获取所有网络适配器信息
  - 返回适配器信息列表

- `bool findAdapterByIP(const std::string& ipAddress, AdapterInfo& adapterInfo)`
  - 根据IP地址查找适配器信息
  - 返回是否找到，找到的信息通过引用参数返回

- `std::string getLastError() const`
  - 获取最后一次操作的错误信息

#### 静态方法

- `static std::string resultToString(NetworkCheckResult result)`
  - 将检查结果转换为可读字符串

- `static bool isValidIPAddress(const std::string& ipAddress)`
  - 验证IP地址格式是否正确

### 枚举类型

#### NetworkCheckResult
- `SUCCESS`: 成功找到IP且连接正常
- `IP_NOT_FOUND`: 未找到指定IP
- `ADAPTER_DISCONNECTED`: 找到IP但适配器未连接
- `ADAPTER_DISABLED`: 找到IP但适配器被禁用
- `SYSTEM_ERROR`: 系统错误
- `INVALID_IP`: 无效的IP地址格式

### 数据结构

#### AdapterInfo
```cpp
struct AdapterInfo {
    std::string name;           // 适配器名称
    std::string description;    // 适配器描述
    std::string ipAddress;      // IP地址
    std::string subnetMask;     // 子网掩码
    bool isConnected;           // 连接状态
    bool isEnabled;             // 启用状态
};
```

## 命令行工具

项目还提供了一个命令行工具用于测试和演示：

```bash
# 列出所有网络适配器
./CheckNetworkDemo --list

# 检查指定IP的适配器状态
./CheckNetworkDemo --check *************

# 查找指定IP的适配器信息
./CheckNetworkDemo --find *********

# 显示帮助信息
./CheckNetworkDemo --help
```

## 测试

项目包含完整的单元测试，测试用例包括：

- IP地址格式验证
- 有效和无效IP地址处理
- 适配器信息获取
- 连接状态检测
- 错误处理机制
- 性能测试

运行测试：
```bash
cd build
ctest --verbose
```

## 平台特定实现

### Windows
- 使用 IP Helper API (`iphlpapi.lib`) 获取适配器信息
- 使用 `GetAdaptersInfo` 获取IP地址信息
- 使用 `GetIfTable` 检查连接状态

### Linux
- 使用 `getifaddrs` 获取网络接口信息
- 使用 `ioctl` 系统调用检查接口状态
- 读取 `/sys/class/net/` 下的文件获取连接状态

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交问题报告和功能请求。如需贡献代码，请：

1. Fork 本项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 发送邮件至：[<EMAIL>]
