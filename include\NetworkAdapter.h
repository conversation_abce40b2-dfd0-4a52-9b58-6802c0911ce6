#pragma once

#include <string>
#include <vector>
#include <memory>

/**
 * @brief Network adapter information structure
 */
struct AdapterInfo {
    std::string name;           // Adapter name
    std::string description;    // Adapter description
    std::string ipAddress;      // IP address
    std::string subnetMask;     // Subnet mask
    bool isConnected;           // Connection status
    bool isEnabled;             // Enabled status
};

/**
 * @brief Network adapter detection result enumeration
 */
enum class NetworkCheckResult {
    SUCCESS,                    // Successfully found IP and connected
    IP_NOT_FOUND,              // Specified IP not found
    ADAPTER_DISCONNECTED,      // Found IP but adapter not connected
    ADAPTER_DISABLED,          // Found IP but adapter disabled
    SYSTEM_ERROR,              // System error
    INVALID_IP                 // Invalid IP address format
};

/**
 * @brief Cross-platform network adapter detection class
 *
 * This class provides cross-platform network adapter detection functionality:
 * 1. Detect if a network adapter with specified IP address exists in the system
 * 2. Determine the connection status of that adapter
 * 3. Return detailed detection results and error information
 */
class NetworkAdapter {
public:
    /**
     * @brief Constructor
     */
    NetworkAdapter();

    /**
     * @brief Destructor
     */
    ~NetworkAdapter();

    /**
     * @brief Check if network adapter with specified IP address exists and is connected
     *
     * @param ipAddress IP address to check
     * @return NetworkCheckResult Check result
     */
    NetworkCheckResult checkNetworkAdapter(const std::string& ipAddress);

    /**
     * @brief Get all network adapter information
     *
     * @return std::vector<AdapterInfo> List of adapter information
     */
    std::vector<AdapterInfo> getAllAdapters();

    /**
     * @brief Find adapter information by IP address
     *
     * @param ipAddress IP address to find
     * @param adapterInfo Output parameter, found adapter information
     * @return bool Whether found
     */
    bool findAdapterByIP(const std::string& ipAddress, AdapterInfo& adapterInfo);

    /**
     * @brief Get error information from last operation
     *
     * @return std::string Error information
     */
    std::string getLastError() const;

    /**
     * @brief Convert check result to string description
     *
     * @param result Check result
     * @return std::string Result description
     */
    static std::string resultToString(NetworkCheckResult result);

    /**
     * @brief Validate if IP address format is correct
     *
     * @param ipAddress IP address string
     * @return bool Whether it's a valid IP address
     */
    static bool isValidIPAddress(const std::string& ipAddress);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;  // PIMPL pattern, hide platform-specific implementation

    /**
     * @brief Platform-specific adapter information retrieval implementation
     *
     * @return std::vector<AdapterInfo> List of adapter information
     */
    std::vector<AdapterInfo> getAdaptersImpl();

    /**
     * @brief Platform-specific connection status check implementation
     *
     * @param adapterName Adapter name
     * @return bool Whether connected
     */
    bool isAdapterConnectedImpl(const std::string& adapterName);
};
