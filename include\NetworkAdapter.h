#pragma once

#include <string>
#include <vector>
#include <memory>

/**
 * @brief 网络适配器信息结构体
 */
struct AdapterInfo {
    std::string name;           // 适配器名称
    std::string description;    // 适配器描述
    std::string ipAddress;      // IP 地址
    std::string subnetMask;     // 子网掩码
    bool isConnected;           // 连接状态
    bool isEnabled;             // 启用状态
};

/**
 * @brief 网络适配器检测结果枚举
 */
enum class NetworkCheckResult {
    SUCCESS,                    // 成功找到 IP 并已连接
    IP_NOT_FOUND,              // 未找到指定的 IP
    ADAPTER_DISCONNECTED,      // 找到 IP 但适配器未连接
    ADAPTER_DISABLED,          // 找到 IP 但适配器已禁用
    SYSTEM_ERROR,              // 系统错误
    INVALID_IP                 // IP 地址格式无效
};

/**
 * @brief 跨平台网络适配器检测类
 *
 * 此类提供跨平台网络适配器检测功能：
 * 1. 检测系统中是否存在具有指定 IP 地址的网络适配器
 * 2. 确定该适配器的连接状态
 * 3. 返回详细的检测结果和错误信息
 */
class NetworkAdapter {
public:
    /**
     * @brief 构造函数
     */
    NetworkAdapter();

    /**
     * @brief 析构函数
     */
    ~NetworkAdapter();

    /**
     * @brief 检查具有指定 IP 地址的网络适配器是否存在且已连接
     *
     * @param ipAddress 要检查的 IP 地址
     * @return NetworkCheckResult 检查结果
     */
    NetworkCheckResult checkNetworkAdapter(const std::string& ipAddress);

    /**
     * @brief 获取所有网络适配器信息
     *
     * @return std::vector<AdapterInfo> 适配器信息列表
     */
    std::vector<AdapterInfo> getAllAdapters();

    /**
     * @brief 根据 IP 地址查找适配器信息
     *
     * @param ipAddress 要查找的 IP 地址
     * @param adapterInfo 输出参数，找到的适配器信息
     * @return bool 是否找到
     */
    bool findAdapterByIP(const std::string& ipAddress, AdapterInfo& adapterInfo);

    /**
     * @brief 获取上次操作的错误信息
     *
     * @return std::string 错误信息
     */
    std::string getLastError() const;

    /**
     * @brief 将检查结果转换为字符串描述
     *
     * @param result 检查结果
     * @return std::string 结果描述
     */
    static std::string resultToString(NetworkCheckResult result);

    /**
     * @brief 验证 IP 地址格式是否正确
     *
     * @param ipAddress IP 地址字符串
     * @return bool 是否是有效的 IP 地址
     */
    static bool isValidIPAddress(const std::string& ipAddress);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;  // PIMPL 模式，隐藏平台特定的实现

    /**
     * @brief 平台特定的适配器信息检索实现
     *
     * @return std::vector<AdapterInfo> 适配器信息列表
     */
    std::vector<AdapterInfo> getAdaptersImpl();

    /**
     * @brief 平台特定的连接状态检查实现
     *
     * @param adapterName 适配器名称
     * @return bool 是否已连接
     */
    bool isAdapterConnectedImpl(const std::string& adapterName);
};
