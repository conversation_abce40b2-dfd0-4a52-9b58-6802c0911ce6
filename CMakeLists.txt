cmake_minimum_required(VERSION 3.16)
project(CheckNetwork VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(MSVC)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 包含目录
include_directories(include)

# 平台特定的库和定义
if(WIN32)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    set(PLATFORM_LIBS ws2_32 iphlpapi wbemuuid ole32 oleaut32)
elseif(UNIX AND NOT APPLE)
    set(PLATFORM_LIBS)
endif()

# 源文件
set(SOURCES
    src/NetworkAdapter.cpp
)

# 平台特定的源文件
if(WIN32)
    list(APPEND SOURCES src/NetworkAdapter_Windows.cpp)
elseif(UNIX AND NOT APPLE)
    list(APPEND SOURCES src/NetworkAdapter_Linux.cpp)
endif()

# 创建静态库
add_library(CheckNetwork STATIC ${SOURCES})
target_link_libraries(CheckNetwork ${PLATFORM_LIBS})

# 创建可执行文件（示例程序）
add_executable(CheckNetworkDemo src/main.cpp)
target_link_libraries(CheckNetworkDemo CheckNetwork)

# 启用测试
enable_testing()

# 查找Google Test
find_package(GTest QUIET)
if(GTest_FOUND)
    add_subdirectory(tests)
else()
    message(STATUS "Google Test not found. Tests will not be built.")
    message(STATUS "To build tests, install Google Test or use: git submodule add https://github.com/google/googletest.git third_party/googletest")
endif()

# 安装规则
install(TARGETS CheckNetwork CheckNetworkDemo
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/ DESTINATION include)
