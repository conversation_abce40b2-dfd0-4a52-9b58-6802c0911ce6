D:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\SimpleNetworkTest.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\SimpleNetworkTest.dir/objects.a @CMakeFiles\SimpleNetworkTest.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\SimpleNetworkTest.dir/objects.a -Wl,--no-whole-archive -o SimpleNetworkTest.exe -Wl,--out-implib,libSimpleNetworkTest.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\SimpleNetworkTest.dir\linkLibs.rsp
