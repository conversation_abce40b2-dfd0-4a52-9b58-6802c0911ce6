﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{92641754-FD91-32F0-8D8F-077F6F10C65B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\workspace\vscode\CheckNetwork\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\workspace\vscode\CheckNetwork\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\workspace\vscode\CheckNetwork\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>E:\workspace\vscode\CheckNetwork\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\workspace\vscode\CheckNetwork\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/workspace/vscode/CheckNetwork/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\msys64\mingw64\bin\cmake.exe -SE:/workspace/vscode/CheckNetwork -BE:/workspace/vscode/CheckNetwork/build --check-stamp-file E:/workspace/vscode/CheckNetwork/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\msys64\mingw64\lib\cmake\GTest\GTestConfig.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestConfigVersion.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets-release.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCXXInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCommonLanguageInclude.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeFindDependencyMacro.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeGenericSystem.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeInitializeConfigs.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeLanguageInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeRCInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInitialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\FindGTest.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageHandleStandardArgs.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageMessage.cmake;D:\msys64\mingw64\share\cmake\Modules\GoogleTest.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-Initialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\WindowsPaths.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\workspace\vscode\CheckNetwork\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/workspace/vscode/CheckNetwork/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\msys64\mingw64\bin\cmake.exe -SE:/workspace/vscode/CheckNetwork -BE:/workspace/vscode/CheckNetwork/build --check-stamp-file E:/workspace/vscode/CheckNetwork/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\msys64\mingw64\lib\cmake\GTest\GTestConfig.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestConfigVersion.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets-release.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCXXInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCommonLanguageInclude.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeFindDependencyMacro.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeGenericSystem.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeInitializeConfigs.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeLanguageInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeRCInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInitialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\FindGTest.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageHandleStandardArgs.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageMessage.cmake;D:\msys64\mingw64\share\cmake\Modules\GoogleTest.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-Initialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\WindowsPaths.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\workspace\vscode\CheckNetwork\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/workspace/vscode/CheckNetwork/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\msys64\mingw64\bin\cmake.exe -SE:/workspace/vscode/CheckNetwork -BE:/workspace/vscode/CheckNetwork/build --check-stamp-file E:/workspace/vscode/CheckNetwork/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\msys64\mingw64\lib\cmake\GTest\GTestConfig.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestConfigVersion.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets-release.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCXXInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCommonLanguageInclude.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeFindDependencyMacro.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeGenericSystem.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeInitializeConfigs.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeLanguageInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeRCInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInitialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\FindGTest.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageHandleStandardArgs.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageMessage.cmake;D:\msys64\mingw64\share\cmake\Modules\GoogleTest.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-Initialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\WindowsPaths.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\workspace\vscode\CheckNetwork\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/workspace/vscode/CheckNetwork/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\msys64\mingw64\bin\cmake.exe -SE:/workspace/vscode/CheckNetwork -BE:/workspace/vscode/CheckNetwork/build --check-stamp-file E:/workspace/vscode/CheckNetwork/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\msys64\mingw64\lib\cmake\GTest\GTestConfig.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestConfigVersion.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets-release.cmake;D:\msys64\mingw64\lib\cmake\GTest\GTestTargets.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCXXInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeCommonLanguageInclude.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeFindDependencyMacro.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeGenericSystem.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeInitializeConfigs.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeLanguageInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeRCInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\CMakeSystemSpecificInitialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Compiler\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\FindGTest.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageHandleStandardArgs.cmake;D:\msys64\mingw64\share\cmake\Modules\FindPackageMessage.cmake;D:\msys64\mingw64\share\cmake\Modules\GoogleTest.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCXXLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Internal\CMakeCommonLinkerInformation.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Linker\MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Linker\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-Initialize.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC-CXX.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows-MSVC.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\Windows.cmake;D:\msys64\mingw64\share\cmake\Modules\Platform\WindowsPaths.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;E:\workspace\vscode\CheckNetwork\build\CMakeFiles\4.0.3\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\workspace\vscode\CheckNetwork\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="E:\workspace\vscode\CheckNetwork\build\ZERO_CHECK.vcxproj">
      <Project>{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\workspace\vscode\CheckNetwork\build\CheckNetwork.vcxproj">
      <Project>{98D4B78D-A228-3B9D-995E-3CC096F768FF}</Project>
      <Name>CheckNetwork</Name>
    </ProjectReference>
    <ProjectReference Include="E:\workspace\vscode\CheckNetwork\build\CheckNetworkDemo.vcxproj">
      <Project>{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}</Project>
      <Name>CheckNetworkDemo</Name>
    </ProjectReference>
    <ProjectReference Include="E:\workspace\vscode\CheckNetwork\build\tests\CheckNetworkTests.vcxproj">
      <Project>{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}</Project>
      <Name>CheckNetworkTests</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>