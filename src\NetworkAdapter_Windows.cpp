#ifdef _WIN32

#include "NetworkAdapter.h"
#include <windows.h>
#include <iphlpapi.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <memory>

#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")

std::vector<AdapterInfo> NetworkAdapter::getAdaptersImpl() {
    std::vector<AdapterInfo> adapters;

    // Get buffer size needed for adapter information
    ULONG bufferSize = 0;
    DWORD result = GetAdaptersInfo(nullptr, &bufferSize);

    if (result != ERROR_BUFFER_OVERFLOW) {
        throw std::runtime_error("Failed to get adapter info buffer size");
    }

    // Allocate buffer
    auto buffer = std::make_unique<char[]>(bufferSize);
    PIP_ADAPTER_INFO pAdapterInfo = reinterpret_cast<PIP_ADAPTER_INFO>(buffer.get());

    // Get adapter information
    result = GetAdaptersInfo(pAdapterInfo, &bufferSize);
    if (result != NO_ERROR) {
        throw std::runtime_error("Failed to get adapter information");
    }

    // Iterate through adapters
    PIP_ADAPTER_INFO pAdapter = pAdapterInfo;
    while (pAdapter) {
        AdapterInfo info;
        info.name = pAdapter->AdapterName;
        info.description = pAdapter->Description;

        // Get IP address information
        PIP_ADDR_STRING pIpAddr = &pAdapter->IpAddressList;
        while (pIpAddr) {
            if (strlen(pIpAddr->IpAddress.String) > 0 &&
                strcmp(pIpAddr->IpAddress.String, "0.0.0.0") != 0) {

                AdapterInfo adapterWithIP = info;
                adapterWithIP.ipAddress = pIpAddr->IpAddress.String;
                adapterWithIP.subnetMask = pIpAddr->IpMask.String;

                // Check connection status
                adapterWithIP.isConnected = isAdapterConnectedImpl(pAdapter->AdapterName);
                adapterWithIP.isEnabled = true; // If we can get info, usually means enabled

                adapters.push_back(adapterWithIP);
            }
            pIpAddr = pIpAddr->Next;
        }

        pAdapter = pAdapter->Next;
    }

    return adapters;
}

bool NetworkAdapter::isAdapterConnectedImpl(const std::string& adapterName) {
    // Get interface table
    PMIB_IFTABLE pIfTable = nullptr;
    DWORD dwSize = 0;

    // Get required buffer size
    DWORD result = GetIfTable(pIfTable, &dwSize, FALSE);
    if (result == ERROR_INSUFFICIENT_BUFFER) {
        pIfTable = reinterpret_cast<PMIB_IFTABLE>(malloc(dwSize));
        if (pIfTable == nullptr) {
            return false;
        }
    } else {
        return false;
    }

    // Get interface table
    result = GetIfTable(pIfTable, &dwSize, FALSE);
    if (result != NO_ERROR) {
        free(pIfTable);
        return false;
    }

    bool isConnected = false;

    // Iterate through interfaces to find matching adapter
    for (DWORD i = 0; i < pIfTable->dwNumEntries; i++) {
        MIB_IFROW& ifRow = pIfTable->table[i];

        // Check if interface description contains adapter name
        std::string ifDesc(reinterpret_cast<char*>(ifRow.bDescr), ifRow.dwDescrLen);

        // Simplified matching logic: check operational status
        if (ifRow.dwOperStatus == IF_OPER_STATUS_OPERATIONAL) {
            // Further check if it's ethernet interface and connected
            if (ifRow.dwType == IF_TYPE_ETHERNET_CSMACD ||
                ifRow.dwType == IF_TYPE_IEEE80211) {

                // Check admin status and operational status
                if (ifRow.dwAdminStatus == 1 && ifRow.dwOperStatus == IF_OPER_STATUS_OPERATIONAL) {
                    isConnected = true;
                    break;
                }
            }
        }
    }

    free(pIfTable);
    return isConnected;
}

#endif // _WIN32
