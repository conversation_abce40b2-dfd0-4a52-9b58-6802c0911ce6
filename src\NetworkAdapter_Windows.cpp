#ifdef _WIN32

#include "NetworkAdapter.h"
#include <windows.h>
#include <iphlpapi.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <memory>

std::vector<AdapterInfo> NetworkAdapter::getAdaptersImpl() {
    std::vector<AdapterInfo> adapters;

    // 获取适配器信息所需的缓冲区大小
    ULONG bufferSize = 0;
    DWORD result = GetAdaptersInfo(nullptr, &bufferSize);

    if (result != ERROR_BUFFER_OVERFLOW) {
        throw std::runtime_error("获取适配器信息缓冲区大小失败");
    }

    // 分配缓冲区
    auto buffer = std::make_unique<char[]>(bufferSize);
    PIP_ADAPTER_INFO pAdapterInfo = reinterpret_cast<PIP_ADAPTER_INFO>(buffer.get());

    // 获取适配器信息
    result = GetAdaptersInfo(pAdapterInfo, &bufferSize);
    if (result != NO_ERROR) {
        throw std::runtime_error("获取适配器信息失败");
    }

    // 遍历适配器
    PIP_ADAPTER_INFO pAdapter = pAdapterInfo;
    while (pAdapter) {
        AdapterInfo info;
        info.name = pAdapter->AdapterName;
        info.description = pAdapter->Description;

        // 获取 IP 地址信息
        PIP_ADDR_STRING pIpAddr = &pAdapter->IpAddressList;
        while (pIpAddr) {
            if (strlen(pIpAddr->IpAddress.String) > 0 &&
                strcmp(pIpAddr->IpAddress.String, "0.0.0.0") != 0) {

                AdapterInfo adapterWithIP = info;
                adapterWithIP.ipAddress = pIpAddr->IpAddress.String;
                adapterWithIP.subnetMask = pIpAddr->IpMask.String;

                // 检查连接状态
                adapterWithIP.isConnected = isAdapterConnectedImpl(pAdapter->AdapterName);
                adapterWithIP.isEnabled = true; // 如果能获取到信息，通常表示已启用

                adapters.push_back(adapterWithIP);
            }
            pIpAddr = pIpAddr->Next;
        }

        pAdapter = pAdapter->Next;
    }

    return adapters;
}

bool NetworkAdapter::isAdapterConnectedImpl(const std::string& adapterName) {
    // 获取接口表
    PMIB_IFTABLE pIfTable = nullptr;
    DWORD dwSize = 0;

    // 获取所需的缓冲区大小
    DWORD result = GetIfTable(pIfTable, &dwSize, FALSE);
    if (result == ERROR_INSUFFICIENT_BUFFER) {
        pIfTable = reinterpret_cast<PMIB_IFTABLE>(malloc(dwSize));
        if (pIfTable == nullptr) {
            return false;
        }
    } else {
        return false;
    }

    // 获取接口表
    result = GetIfTable(pIfTable, &dwSize, FALSE);
    if (result != NO_ERROR) {
        free(pIfTable);
        return false;
    }

    bool isConnected = false;

    // 遍历接口以查找匹配的适配器
    for (DWORD i = 0; i < pIfTable->dwNumEntries; i++) {
        MIB_IFROW& ifRow = pIfTable->table[i];

        // 检查接口描述是否包含适配器名称
        std::string ifDesc(reinterpret_cast<char*>(ifRow.bDescr), ifRow.dwDescrLen);

        // 简化匹配逻辑：检查操作状态
        if (ifRow.dwOperStatus == IF_OPER_STATUS_OPERATIONAL) {
            // 进一步检查是否是以太网接口并已连接
            if (ifRow.dwType == IF_TYPE_ETHERNET_CSMACD ||
                ifRow.dwType == IF_TYPE_IEEE80211) {

                // 检查管理状态和操作状态
                if (ifRow.dwAdminStatus == 1 && ifRow.dwOperStatus == IF_OPER_STATUS_OPERATIONAL) {
                    isConnected = true;
                    break;
                }
            }
        }
    }

    free(pIfTable);
    return isConnected;
}

#endif // _WIN32
