# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

# Include any dependencies generated for this target.
include tests/CMakeFiles/CheckNetworkTests.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include tests/CMakeFiles/CheckNetworkTests.dir/compiler_depend.make

# Include the progress variables for this target.
include tests/CMakeFiles/CheckNetworkTests.dir/progress.make

# Include the compile flags for this target's objects.
include tests/CMakeFiles/CheckNetworkTests.dir/flags.make

tests/CMakeFiles/CheckNetworkTests.dir/codegen:
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/codegen

tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj: tests/CMakeFiles/CheckNetworkTests.dir/flags.make
tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj: tests/CMakeFiles/CheckNetworkTests.dir/includes_CXX.rsp
tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj: E:/workspace/vscode/CheckNetwork/tests/test_NetworkAdapter.cpp
tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj: tests/CMakeFiles/CheckNetworkTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj -MF CMakeFiles\CheckNetworkTests.dir\test_NetworkAdapter.cpp.obj.d -o CMakeFiles\CheckNetworkTests.dir\test_NetworkAdapter.cpp.obj -c E:\workspace\vscode\CheckNetwork\tests\test_NetworkAdapter.cpp

tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.i"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\CheckNetwork\tests\test_NetworkAdapter.cpp > CMakeFiles\CheckNetworkTests.dir\test_NetworkAdapter.cpp.i

tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.s"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\CheckNetwork\tests\test_NetworkAdapter.cpp -o CMakeFiles\CheckNetworkTests.dir\test_NetworkAdapter.cpp.s

tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj: tests/CMakeFiles/CheckNetworkTests.dir/flags.make
tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj: tests/CMakeFiles/CheckNetworkTests.dir/includes_CXX.rsp
tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj: E:/workspace/vscode/CheckNetwork/tests/test_main.cpp
tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj: tests/CMakeFiles/CheckNetworkTests.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj -MF CMakeFiles\CheckNetworkTests.dir\test_main.cpp.obj.d -o CMakeFiles\CheckNetworkTests.dir\test_main.cpp.obj -c E:\workspace\vscode\CheckNetwork\tests\test_main.cpp

tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CheckNetworkTests.dir/test_main.cpp.i"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\CheckNetwork\tests\test_main.cpp > CMakeFiles\CheckNetworkTests.dir\test_main.cpp.i

tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CheckNetworkTests.dir/test_main.cpp.s"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\CheckNetwork\tests\test_main.cpp -o CMakeFiles\CheckNetworkTests.dir\test_main.cpp.s

# Object files for target CheckNetworkTests
CheckNetworkTests_OBJECTS = \
"CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj" \
"CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj"

# External object files for target CheckNetworkTests
CheckNetworkTests_EXTERNAL_OBJECTS =

tests/CheckNetworkTests.exe: tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj
tests/CheckNetworkTests.exe: tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj
tests/CheckNetworkTests.exe: tests/CMakeFiles/CheckNetworkTests.dir/build.make
tests/CheckNetworkTests.exe: libCheckNetwork.a
tests/CheckNetworkTests.exe: D:/msys64/mingw64/lib/libgtest_main.dll.a
tests/CheckNetworkTests.exe: D:/msys64/mingw64/lib/libgtest.dll.a
tests/CheckNetworkTests.exe: tests/CMakeFiles/CheckNetworkTests.dir/linkLibs.rsp
tests/CheckNetworkTests.exe: tests/CMakeFiles/CheckNetworkTests.dir/objects1.rsp
tests/CheckNetworkTests.exe: tests/CMakeFiles/CheckNetworkTests.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable CheckNetworkTests.exe"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\CheckNetworkTests.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
tests/CMakeFiles/CheckNetworkTests.dir/build: tests/CheckNetworkTests.exe
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/build

tests/CMakeFiles/CheckNetworkTests.dir/clean:
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && $(CMAKE_COMMAND) -P CMakeFiles\CheckNetworkTests.dir\cmake_clean.cmake
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/clean

tests/CMakeFiles/CheckNetworkTests.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\CheckNetwork E:\workspace\vscode\CheckNetwork\tests E:\workspace\vscode\CheckNetwork\build E:\workspace\vscode\CheckNetwork\build\tests E:\workspace\vscode\CheckNetwork\build\tests\CMakeFiles\CheckNetworkTests.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/depend

