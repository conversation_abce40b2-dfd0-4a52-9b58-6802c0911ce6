
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/workspace/vscode/CheckNetwork/src/NetworkAdapter.cpp" "CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj" "gcc" "CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj.d"
  "E:/workspace/vscode/CheckNetwork/src/NetworkAdapter_Windows.cpp" "CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj" "gcc" "CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
