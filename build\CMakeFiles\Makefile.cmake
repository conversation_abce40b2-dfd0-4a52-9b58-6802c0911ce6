# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "D:/msys64/mingw64/lib/cmake/GTest/GTestConfig.cmake"
  "D:/msys64/mingw64/lib/cmake/GTest/GTestConfigVersion.cmake"
  "D:/msys64/mingw64/lib/cmake/GTest/GTestTargets-release.cmake"
  "D:/msys64/mingw64/lib/cmake/GTest/GTestTargets.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeCXXInformation.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/FindGTest.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/FindPackageMessage.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/GoogleTest.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake"
  "D:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake"
  "E:/workspace/vscode/CheckNetwork/CMakeLists.txt"
  "CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeRCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "tests/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/CheckNetwork.dir/DependInfo.cmake"
  "CMakeFiles/CheckNetworkDemo.dir/DependInfo.cmake"
  "tests/CMakeFiles/SimpleNetworkTest.dir/DependInfo.cmake"
  "tests/CMakeFiles/CheckNetworkTests.dir/DependInfo.cmake"
  )
