# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

# Include any dependencies generated for this target.
include CMakeFiles/CheckNetworkDemo.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CheckNetworkDemo.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CheckNetworkDemo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CheckNetworkDemo.dir/flags.make

CMakeFiles/CheckNetworkDemo.dir/codegen:
.PHONY : CMakeFiles/CheckNetworkDemo.dir/codegen

CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj: CMakeFiles/CheckNetworkDemo.dir/flags.make
CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj: CMakeFiles/CheckNetworkDemo.dir/includes_CXX.rsp
CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj: E:/workspace/vscode/CheckNetwork/src/main.cpp
CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj: CMakeFiles/CheckNetworkDemo.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj -MF CMakeFiles\CheckNetworkDemo.dir\src\main.cpp.obj.d -o CMakeFiles\CheckNetworkDemo.dir\src\main.cpp.obj -c E:\workspace\vscode\CheckNetwork\src\main.cpp

CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\CheckNetwork\src\main.cpp > CMakeFiles\CheckNetworkDemo.dir\src\main.cpp.i

CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\CheckNetwork\src\main.cpp -o CMakeFiles\CheckNetworkDemo.dir\src\main.cpp.s

# Object files for target CheckNetworkDemo
CheckNetworkDemo_OBJECTS = \
"CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj"

# External object files for target CheckNetworkDemo
CheckNetworkDemo_EXTERNAL_OBJECTS =

CheckNetworkDemo.exe: CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj
CheckNetworkDemo.exe: CMakeFiles/CheckNetworkDemo.dir/build.make
CheckNetworkDemo.exe: libCheckNetwork.a
CheckNetworkDemo.exe: CMakeFiles/CheckNetworkDemo.dir/linkLibs.rsp
CheckNetworkDemo.exe: CMakeFiles/CheckNetworkDemo.dir/objects1.rsp
CheckNetworkDemo.exe: CMakeFiles/CheckNetworkDemo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable CheckNetworkDemo.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\CheckNetworkDemo.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CheckNetworkDemo.dir/build: CheckNetworkDemo.exe
.PHONY : CMakeFiles/CheckNetworkDemo.dir/build

CMakeFiles/CheckNetworkDemo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\CheckNetworkDemo.dir\cmake_clean.cmake
.PHONY : CMakeFiles/CheckNetworkDemo.dir/clean

CMakeFiles/CheckNetworkDemo.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\CheckNetwork E:\workspace\vscode\CheckNetwork E:\workspace\vscode\CheckNetwork\build E:\workspace\vscode\CheckNetwork\build E:\workspace\vscode\CheckNetwork\build\CMakeFiles\CheckNetworkDemo.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/CheckNetworkDemo.dir/depend

