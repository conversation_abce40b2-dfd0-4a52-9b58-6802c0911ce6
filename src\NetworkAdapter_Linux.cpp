#if defined(__linux__) || defined(__unix__)

#include "NetworkAdapter.h"
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <unistd.h>
#include <cstring>
#include <fstream>
#include <sstream>

std::vector<AdapterInfo> NetworkAdapter::getAdaptersImpl() {
    std::vector<AdapterInfo> adapters;
    struct ifaddrs *ifaddr, *ifa;

    // Get network interface addresses
    if (getifaddrs(&ifaddr) == -1) {
        throw std::runtime_error("Failed to get network interfaces");
    }

    // Iterate through interfaces
    for (ifa = ifaddr; ifa != nullptr; ifa = ifa->ifa_next) {
        if (ifa->ifa_addr == nullptr) {
            continue;
        }

        // Only handle IPv4 addresses
        if (ifa->ifa_addr->sa_family == AF_INET) {
            AdapterInfo info;
            info.name = ifa->ifa_name;
            info.description = ifa->ifa_name; // In Linux, name and description are usually the same

            // Get IP address
            struct sockaddr_in* addr_in = reinterpret_cast<struct sockaddr_in*>(ifa->ifa_addr);
            char ip_str[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &(addr_in->sin_addr), ip_str, INET_ADDRSTRLEN);
            info.ipAddress = ip_str;

            // Get subnet mask
            if (ifa->ifa_netmask) {
                struct sockaddr_in* mask_in = reinterpret_cast<struct sockaddr_in*>(ifa->ifa_netmask);
                char mask_str[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &(mask_in->sin_addr), mask_str, INET_ADDRSTRLEN);
                info.subnetMask = mask_str;
            }

            // Check interface status
            info.isEnabled = (ifa->ifa_flags & IFF_UP) != 0;
            info.isConnected = isAdapterConnectedImpl(ifa->ifa_name);

            // Skip loopback interface and invalid IP
            if (!(ifa->ifa_flags & IFF_LOOPBACK) && info.ipAddress != "0.0.0.0") {
                adapters.push_back(info);
            }
        }
    }

    freeifaddrs(ifaddr);
    return adapters;
}

bool NetworkAdapter::isAdapterConnectedImpl(const std::string& adapterName) {
    // Method 1: Check /sys/class/net/interface_name/carrier file
    std::string carrierPath = "/sys/class/net/" + adapterName + "/carrier";
    std::ifstream carrierFile(carrierPath);

    if (carrierFile.is_open()) {
        std::string carrier;
        std::getline(carrierFile, carrier);
        carrierFile.close();

        // carrier=1 means connected, 0 means disconnected
        if (carrier == "1") {
            return true;
        }
    }

    // Method 2: Use ioctl to check interface status
    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        return false;
    }

    struct ifreq ifr;
    memset(&ifr, 0, sizeof(ifr));
    strncpy(ifr.ifr_name, adapterName.c_str(), IFNAMSIZ - 1);

    bool isConnected = false;

    // Get interface flags
    if (ioctl(sockfd, SIOCGIFFLAGS, &ifr) == 0) {
        // Check if interface is UP and RUNNING
        if ((ifr.ifr_flags & IFF_UP) && (ifr.ifr_flags & IFF_RUNNING)) {
            isConnected = true;
        }
    }

    close(sockfd);

    // Method 3: For wireless interfaces, check if there's an associated AP
    if (!isConnected && adapterName.find("wl") == 0) {
        // Check wireless interface status
        std::string operstatePath = "/sys/class/net/" + adapterName + "/operstate";
        std::ifstream operstateFile(operstatePath);

        if (operstateFile.is_open()) {
            std::string operstate;
            std::getline(operstateFile, operstate);
            operstateFile.close();

            // operstate="up" means connected
            if (operstate == "up") {
                isConnected = true;
            }
        }
    }

    return isConnected;
}

#endif // defined(__linux__) || defined(__unix__)
