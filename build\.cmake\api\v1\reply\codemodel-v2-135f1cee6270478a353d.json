{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-dc31f692287bf8438b7c.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}, {"build": "tests", "jsonFile": "directory-tests-Debug-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "CheckNetwork", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "CheckNetwork::@6890427a1f51a3e7e1df", "jsonFile": "target-CheckNetwork-Debug-57dbd7cffa6e3a0d78c9.json", "name": "CheckNetwork", "projectIndex": 0}, {"directoryIndex": 0, "id": "CheckNetworkDemo::@6890427a1f51a3e7e1df", "jsonFile": "target-CheckNetworkDemo-Debug-5827cb1e37a2a13882d3.json", "name": "CheckNetworkDemo", "projectIndex": 0}, {"directoryIndex": 1, "id": "CheckNetworkTests::@a44f0ac069e85531cdee", "jsonFile": "target-CheckNetworkTests-Debug-c14697f50a1f4b277d19.json", "name": "CheckNetworkTests", "projectIndex": 0}, {"directoryIndex": 1, "id": "SimpleNetworkTest::@a44f0ac069e85531cdee", "jsonFile": "target-SimpleNetworkTest-Debug-94b7a273925761146800.json", "name": "SimpleNetworkTest", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/workspace/vscode/CheckNetwork/build", "source": "E:/workspace/vscode/CheckNetwork"}, "version": {"major": 2, "minor": 8}}