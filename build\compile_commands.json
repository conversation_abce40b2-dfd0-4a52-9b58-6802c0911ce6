[{"directory": "E:/workspace/vscode/CheckNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe -DWIN32_LEAN_AND_MEAN @CMakeFiles/CheckNetwork.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -pedantic -o CMakeFiles\\CheckNetwork.dir\\src\\NetworkAdapter.cpp.obj -c E:\\workspace\\vscode\\CheckNetwork\\src\\NetworkAdapter.cpp", "file": "E:/workspace/vscode/CheckNetwork/src/NetworkAdapter.cpp", "output": "CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj"}, {"directory": "E:/workspace/vscode/CheckNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe -DWIN32_LEAN_AND_MEAN @CMakeFiles/CheckNetwork.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -pedantic -o CMakeFiles\\CheckNetwork.dir\\src\\NetworkAdapter_Windows.cpp.obj -c E:\\workspace\\vscode\\CheckNetwork\\src\\NetworkAdapter_Windows.cpp", "file": "E:/workspace/vscode/CheckNetwork/src/NetworkAdapter_Windows.cpp", "output": "CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj"}, {"directory": "E:/workspace/vscode/CheckNetwork/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe -DWIN32_LEAN_AND_MEAN @CMakeFiles/CheckNetworkDemo.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -pedantic -o CMakeFiles\\CheckNetworkDemo.dir\\src\\main.cpp.obj -c E:\\workspace\\vscode\\CheckNetwork\\src\\main.cpp", "file": "E:/workspace/vscode/CheckNetwork/src/main.cpp", "output": "CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj"}, {"directory": "E:/workspace/vscode/CheckNetwork/build/tests", "command": "D:\\msys64\\mingw64\\bin\\g++.exe -DWIN32_LEAN_AND_MEAN @CMakeFiles/SimpleNetworkTest.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -pedantic -o CMakeFiles\\SimpleNetworkTest.dir\\simple_test.cpp.obj -c E:\\workspace\\vscode\\CheckNetwork\\tests\\simple_test.cpp", "file": "E:/workspace/vscode/CheckNetwork/tests/simple_test.cpp", "output": "tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj"}, {"directory": "E:/workspace/vscode/CheckNetwork/build/tests", "command": "D:\\msys64\\mingw64\\bin\\g++.exe -DGTEST_LINKED_AS_SHARED_LIBRARY=1 -DWIN32_LEAN_AND_MEAN @CMakeFiles/CheckNetworkTests.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -pedantic -o CMakeFiles\\CheckNetworkTests.dir\\test_NetworkAdapter.cpp.obj -c E:\\workspace\\vscode\\CheckNetwork\\tests\\test_NetworkAdapter.cpp", "file": "E:/workspace/vscode/CheckNetwork/tests/test_NetworkAdapter.cpp", "output": "tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj"}, {"directory": "E:/workspace/vscode/CheckNetwork/build/tests", "command": "D:\\msys64\\mingw64\\bin\\g++.exe -DGTEST_LINKED_AS_SHARED_LIBRARY=1 -DWIN32_LEAN_AND_MEAN @CMakeFiles/CheckNetworkTests.dir/includes_CXX.rsp -g -std=gnu++17 -Wall -Wextra -pedantic -o CMakeFiles\\CheckNetworkTests.dir\\test_main.cpp.obj -c E:\\workspace\\vscode\\CheckNetwork\\tests\\test_main.cpp", "file": "E:/workspace/vscode/CheckNetwork/tests/test_main.cpp", "output": "tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj"}]