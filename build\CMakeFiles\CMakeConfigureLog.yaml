
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
      版权所有(C) Microsoft Corporation。保留所有权利。
      
      生成启动时间为 2025/7/14 14:13:36。
      节点 1 上的项目“E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)。
      PrepareForBuild:
        正在创建目录“Debug\\”。
        正在创建目录“Debug\\CompilerIdCXX.tlog\\”。
      InitializeBuildStatus:
        正在创建“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "E:\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        D:\\Microsoft Visual Studio\\2019\\Enterprise\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /permissive /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        D:\\Microsoft Visual Studio\\2019\\Enterprise\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"E:\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"E:\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "E:\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.exe" "E:\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=D:\\Microsoft Visual Studio\\2019\\Enterprise\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        正在删除文件“Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild”。
        正在对“Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate”执行 Touch 任务。
      已完成生成项目“E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj”(默认目标)的操作。
      
      已成功生成。
          0 个警告
          0 个错误
      
      已用时间 00:00:05.34
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        E:/workspace/vscode/CheckNetwork/build/CMakeFiles/4.0.3/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "E:/workspace/vscode/CheckNetwork/build/CMakeFiles/CMakeScratch/TryCompile-0xlms1"
      binary: "E:/workspace/vscode/CheckNetwork/build/CMakeFiles/CMakeScratch/TryCompile-0xlms1"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/workspace/vscode/CheckNetwork/build/CMakeFiles/CMakeScratch/TryCompile-0xlms1'
        
        Run Build Command(s): "D:/Microsoft Visual Studio/2019/Enterprise/MSBuild/Current/Bin/MSBuild.exe" cmTC_067d5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        用于 .NET Framework 的 Microsoft (R) 生成引擎版本 16.11.6+a918ceb31
        版权所有(C) Microsoft Corporation。保留所有权利。
        
        生成启动时间为 2025/7/14 14:13:44。
        节点 1 上的项目“E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0xlms1\\cmTC_067d5.vcxproj”(默认目标)。
        PrepareForBuild:
          正在创建目录“cmTC_067d5.dir\\Debug\\”。
          正在创建目录“E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0xlms1\\Debug\\”。
          正在创建目录“cmTC_067d5.dir\\Debug\\cmTC_067d5.tlog\\”。
        InitializeBuildStatus:
          正在创建“cmTC_067d5.dir\\Debug\\cmTC_067d5.tlog\\unsuccessfulbuild”，因为已指定“AlwaysCreate”。
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "E:\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          D:\\Microsoft Visual Studio\\2019\\Enterprise\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /I"E:\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /permissive /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_067d5.dir\\Debug\\\\" /Fd"cmTC_067d5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue D:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCXXCompilerABI.cpp
          用于 x64 的 Microsoft (R) C/C++ 优化编译器 19.29.30159 版
          CMakeCXXCompilerABI.cpp
          版权所有(C) Microsoft Corporation。保留所有权利。
          cl /c /I"E:\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /permissive /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_067d5.dir\\Debug\\\\" /Fd"cmTC_067d5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue D:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCXXCompilerABI.cpp
        Link:
          D:\\Microsoft Visual Studio\\2019\\Enterprise\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0xlms1\\Debug\\cmTC_067d5.exe" /INCREMENTAL /ILK:"cmTC_067d5.dir\\Debug\\cmTC_067d5.ilk" /NOLOGO /LIBPATH:"E:\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"E:\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "E:\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"E:/workspace/vscode/CheckNetwork/build/CMakeFiles/CMakeScratch/TryCompile-0xlms1/Debug/cmTC_067d5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"E:/workspace/vscode/CheckNetwork/build/CMakeFiles/CMakeScratch/TryCompile-0xlms1/Debug/cmTC_067d5.lib" /MACHINE:X64  /machine:x64 cmTC_067d5.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_067d5.vcxproj -> E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0xlms1\\Debug\\cmTC_067d5.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "E:\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0xlms1\\Debug\\cmTC_067d5.exe" "E:\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_067d5.dir\\Debug\\cmTC_067d5.tlog\\cmTC_067d5.write.1u.tlog" "cmTC_067d5.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          正在删除文件“cmTC_067d5.dir\\Debug\\cmTC_067d5.tlog\\unsuccessfulbuild”。
          正在对“cmTC_067d5.dir\\Debug\\cmTC_067d5.tlog\\cmTC_067d5.lastbuildstate”执行 Touch 任务。
        已完成生成项目“E:\\workspace\\vscode\\CheckNetwork\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0xlms1\\cmTC_067d5.vcxproj”(默认目标)的操作。
        
        已成功生成。
            0 个警告
            0 个错误
        
        已用时间 00:00:01.99
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': D:/Microsoft Visual Studio/2019/Enterprise/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/msys64/mingw64/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "D:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "D:/Microsoft Visual Studio/2019/Enterprise/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
