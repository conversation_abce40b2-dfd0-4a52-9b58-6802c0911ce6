# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	D:\msys64\mingw64\bin\ctest.exe $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	D:\msys64\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	D:\msys64\mingw64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d E:\workspace\vscode\CheckNetwork\build && $(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles E:\workspace\vscode\CheckNetwork\build\tests\\CMakeFiles\progress.marks
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
tests/CMakeFiles/SimpleNetworkTest.dir/rule:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/CMakeFiles/SimpleNetworkTest.dir/rule
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/rule

# Convenience name for target.
SimpleNetworkTest: tests/CMakeFiles/SimpleNetworkTest.dir/rule
.PHONY : SimpleNetworkTest

# fast build rule for target.
SimpleNetworkTest/fast:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/build
.PHONY : SimpleNetworkTest/fast

# Convenience name for target.
tests/CMakeFiles/CheckNetworkTests.dir/rule:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 tests/CMakeFiles/CheckNetworkTests.dir/rule
.PHONY : tests/CMakeFiles/CheckNetworkTests.dir/rule

# Convenience name for target.
CheckNetworkTests: tests/CMakeFiles/CheckNetworkTests.dir/rule
.PHONY : CheckNetworkTests

# fast build rule for target.
CheckNetworkTests/fast:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/build
.PHONY : CheckNetworkTests/fast

simple_test.obj: simple_test.cpp.obj
.PHONY : simple_test.obj

# target to build an object file
simple_test.cpp.obj:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj
.PHONY : simple_test.cpp.obj

simple_test.i: simple_test.cpp.i
.PHONY : simple_test.i

# target to preprocess a source file
simple_test.cpp.i:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.i
.PHONY : simple_test.cpp.i

simple_test.s: simple_test.cpp.s
.PHONY : simple_test.s

# target to generate assembly for a file
simple_test.cpp.s:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.s
.PHONY : simple_test.cpp.s

test_NetworkAdapter.obj: test_NetworkAdapter.cpp.obj
.PHONY : test_NetworkAdapter.obj

# target to build an object file
test_NetworkAdapter.cpp.obj:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.obj
.PHONY : test_NetworkAdapter.cpp.obj

test_NetworkAdapter.i: test_NetworkAdapter.cpp.i
.PHONY : test_NetworkAdapter.i

# target to preprocess a source file
test_NetworkAdapter.cpp.i:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.i
.PHONY : test_NetworkAdapter.cpp.i

test_NetworkAdapter.s: test_NetworkAdapter.cpp.s
.PHONY : test_NetworkAdapter.s

# target to generate assembly for a file
test_NetworkAdapter.cpp.s:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/test_NetworkAdapter.cpp.s
.PHONY : test_NetworkAdapter.cpp.s

test_main.obj: test_main.cpp.obj
.PHONY : test_main.obj

# target to build an object file
test_main.cpp.obj:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.obj
.PHONY : test_main.cpp.obj

test_main.i: test_main.cpp.i
.PHONY : test_main.i

# target to preprocess a source file
test_main.cpp.i:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.i
.PHONY : test_main.cpp.i

test_main.s: test_main.cpp.s
.PHONY : test_main.s

# target to generate assembly for a file
test_main.cpp.s:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/test_main.cpp.s
.PHONY : test_main.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... CheckNetworkTests
	@echo ... SimpleNetworkTest
	@echo ... simple_test.obj
	@echo ... simple_test.i
	@echo ... simple_test.s
	@echo ... test_NetworkAdapter.obj
	@echo ... test_NetworkAdapter.i
	@echo ... test_NetworkAdapter.s
	@echo ... test_main.obj
	@echo ... test_main.i
	@echo ... test_main.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d E:\workspace\vscode\CheckNetwork\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

