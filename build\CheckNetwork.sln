﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{92641754-FD91-32F0-8D8F-077F6F10C65B}"
	ProjectSection(ProjectDependencies) = postProject
		{98D4B78D-A228-3B9D-995E-3CC096F768FF} = {98D4B78D-A228-3B9D-995E-3CC096F768FF}
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C} = {88EBFF84-2E2E-3CFD-914E-0D22BB21103C}
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929} = {B1618A3E-0A53-3C9C-8FB2-3F94183D7929}
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A} = {34DC5979-C89B-3555-B46A-0FE2E20E2D7A}
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CheckNetwork", "CheckNetwork.vcxproj", "{98D4B78D-A228-3B9D-995E-3CC096F768FF}"
	ProjectSection(ProjectDependencies) = postProject
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CheckNetworkDemo", "CheckNetworkDemo.vcxproj", "{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}"
	ProjectSection(ProjectDependencies) = postProject
		{98D4B78D-A228-3B9D-995E-3CC096F768FF} = {98D4B78D-A228-3B9D-995E-3CC096F768FF}
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CheckNetworkTests", "tests\CheckNetworkTests.vcxproj", "{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}"
	ProjectSection(ProjectDependencies) = postProject
		{98D4B78D-A228-3B9D-995E-3CC096F768FF} = {98D4B78D-A228-3B9D-995E-3CC096F768FF}
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{A238AFB7-B250-36CC-99EE-10DF847D4963}"
	ProjectSection(ProjectDependencies) = postProject
		{92641754-FD91-32F0-8D8F-077F6F10C65B} = {92641754-FD91-32F0-8D8F-077F6F10C65B}
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{9FC31A8E-699A-3504-8162-7B01659D858E}"
	ProjectSection(ProjectDependencies) = postProject
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SimpleNetworkTest", "tests\SimpleNetworkTest.vcxproj", "{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}"
	ProjectSection(ProjectDependencies) = postProject
		{98D4B78D-A228-3B9D-995E-3CC096F768FF} = {98D4B78D-A228-3B9D-995E-3CC096F768FF}
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC} = {DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.Debug|x64.ActiveCfg = Debug|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.Debug|x64.Build.0 = Debug|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.Release|x64.ActiveCfg = Release|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.Release|x64.Build.0 = Release|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{92641754-FD91-32F0-8D8F-077F6F10C65B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.Debug|x64.ActiveCfg = Debug|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.Debug|x64.Build.0 = Debug|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.Release|x64.ActiveCfg = Release|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.Release|x64.Build.0 = Release|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{98D4B78D-A228-3B9D-995E-3CC096F768FF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.Debug|x64.ActiveCfg = Debug|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.Debug|x64.Build.0 = Debug|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.Release|x64.ActiveCfg = Release|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.Release|x64.Build.0 = Release|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{88EBFF84-2E2E-3CFD-914E-0D22BB21103C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.Debug|x64.ActiveCfg = Debug|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.Debug|x64.Build.0 = Debug|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.Release|x64.ActiveCfg = Release|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.Release|x64.Build.0 = Release|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B1618A3E-0A53-3C9C-8FB2-3F94183D7929}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A238AFB7-B250-36CC-99EE-10DF847D4963}.Debug|x64.ActiveCfg = Debug|x64
		{A238AFB7-B250-36CC-99EE-10DF847D4963}.Release|x64.ActiveCfg = Release|x64
		{A238AFB7-B250-36CC-99EE-10DF847D4963}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A238AFB7-B250-36CC-99EE-10DF847D4963}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9FC31A8E-699A-3504-8162-7B01659D858E}.Debug|x64.ActiveCfg = Debug|x64
		{9FC31A8E-699A-3504-8162-7B01659D858E}.Release|x64.ActiveCfg = Release|x64
		{9FC31A8E-699A-3504-8162-7B01659D858E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9FC31A8E-699A-3504-8162-7B01659D858E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.Debug|x64.ActiveCfg = Debug|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.Debug|x64.Build.0 = Debug|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.Release|x64.ActiveCfg = Release|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.Release|x64.Build.0 = Release|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{34DC5979-C89B-3555-B46A-0FE2E20E2D7A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.Debug|x64.ActiveCfg = Debug|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.Debug|x64.Build.0 = Debug|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.Release|x64.ActiveCfg = Release|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.Release|x64.Build.0 = Release|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DED817B1-DCB5-3CFE-B746-21C5DF9A97FC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {ECF65ED1-4471-3DE9-A254-A8C0545775B8}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
