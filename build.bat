@echo off
REM Windows构建脚本

echo ===== CheckNetwork Windows Build Script =====

REM 检查CMake是否安装
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: CMake not found. Please install CMake first.
    pause
    exit /b 1
)

REM 创建构建目录
if not exist build mkdir build
cd build

echo.
echo ===== Configuring project =====
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    echo Error: CMake configuration failed.
    pause
    exit /b 1
)

echo.
echo ===== Building project (Release) =====
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo Error: Build failed.
    pause
    exit /b 1
)

echo.
echo ===== Building project (Debug) =====
cmake --build . --config Debug
if %errorlevel% neq 0 (
    echo Warning: Debug build failed, but Release build succeeded.
)

echo.
echo ===== Build completed successfully! =====
echo.
echo Executables location:
echo   Release: build\Release\CheckNetworkDemo.exe
echo   Debug:   build\Debug\CheckNetworkDemo.exe
echo.
echo To run tests (if Google Test is available):
echo   ctest -C Release --verbose
echo.
echo To run the demo:
echo   .\Release\CheckNetworkDemo.exe --help
echo.

pause
