@echo off
REM CheckNetwork Installation Script for Windows

echo ===== CheckNetwork Installation Script =====

REM Check if build directory exists
if not exist build (
    echo Error: Build directory not found. Please run build.bat first.
    pause
    exit /b 1
)

REM Check if executables exist
if not exist build\Release\CheckNetworkDemo.exe (
    echo Error: CheckNetworkDemo.exe not found. Please build the project first.
    pause
    exit /b 1
)

REM Create installation directory
set INSTALL_DIR=%USERPROFILE%\CheckNetwork
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\bin" mkdir "%INSTALL_DIR%\bin"
if not exist "%INSTALL_DIR%\lib" mkdir "%INSTALL_DIR%\lib"
if not exist "%INSTALL_DIR%\include" mkdir "%INSTALL_DIR%\include"
if not exist "%INSTALL_DIR%\docs" mkdir "%INSTALL_DIR%\docs"

REM Copy executables
echo Copying executables...
copy build\Release\CheckNetworkDemo.exe "%INSTALL_DIR%\bin\" >nul
if exist build\tests\Release\SimpleNetworkTest.exe (
    copy build\tests\Release\SimpleNetworkTest.exe "%INSTALL_DIR%\bin\" >nul
)

REM Copy library
echo Copying library...
copy build\Release\CheckNetwork.lib "%INSTALL_DIR%\lib\" >nul

REM Copy headers
echo Copying headers...
copy include\NetworkAdapter.h "%INSTALL_DIR%\include\" >nul

REM Copy documentation
echo Copying documentation...
copy README.md "%INSTALL_DIR%\docs\" >nul
copy docs\*.md "%INSTALL_DIR%\docs\" >nul

REM Create usage script
echo Creating usage script...
echo @echo off > "%INSTALL_DIR%\bin\checknetwork.bat"
echo REM CheckNetwork Command Line Tool >> "%INSTALL_DIR%\bin\checknetwork.bat"
echo "%INSTALL_DIR%\bin\CheckNetworkDemo.exe" %%* >> "%INSTALL_DIR%\bin\checknetwork.bat"

echo.
echo ===== Installation completed successfully! =====
echo.
echo Installation directory: %INSTALL_DIR%
echo.
echo To use CheckNetwork:
echo   1. Add %INSTALL_DIR%\bin to your PATH environment variable
echo   2. Run: checknetwork --help
echo.
echo Or run directly:
echo   %INSTALL_DIR%\bin\CheckNetworkDemo.exe --help
echo.
echo To run tests:
echo   %INSTALL_DIR%\bin\SimpleNetworkTest.exe
echo.

pause
