# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	D:\msys64\mingw64\bin\ctest.exe $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	D:\msys64\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	D:\msys64\mingw64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles E:\workspace\vscode\CheckNetwork\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\CheckNetwork\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named CheckNetwork

# Build rule for target.
CheckNetwork: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CheckNetwork
.PHONY : CheckNetwork

# fast build rule for target.
CheckNetwork/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/build
.PHONY : CheckNetwork/fast

#=============================================================================
# Target rules for targets named CheckNetworkDemo

# Build rule for target.
CheckNetworkDemo: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CheckNetworkDemo
.PHONY : CheckNetworkDemo

# fast build rule for target.
CheckNetworkDemo/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/build
.PHONY : CheckNetworkDemo/fast

#=============================================================================
# Target rules for targets named SimpleNetworkTest

# Build rule for target.
SimpleNetworkTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 SimpleNetworkTest
.PHONY : SimpleNetworkTest

# fast build rule for target.
SimpleNetworkTest/fast:
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\SimpleNetworkTest.dir\build.make tests/CMakeFiles/SimpleNetworkTest.dir/build
.PHONY : SimpleNetworkTest/fast

#=============================================================================
# Target rules for targets named CheckNetworkTests

# Build rule for target.
CheckNetworkTests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CheckNetworkTests
.PHONY : CheckNetworkTests

# fast build rule for target.
CheckNetworkTests/fast:
	$(MAKE) $(MAKESILENT) -f tests\CMakeFiles\CheckNetworkTests.dir\build.make tests/CMakeFiles/CheckNetworkTests.dir/build
.PHONY : CheckNetworkTests/fast

src/NetworkAdapter.obj: src/NetworkAdapter.cpp.obj
.PHONY : src/NetworkAdapter.obj

# target to build an object file
src/NetworkAdapter.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj
.PHONY : src/NetworkAdapter.cpp.obj

src/NetworkAdapter.i: src/NetworkAdapter.cpp.i
.PHONY : src/NetworkAdapter.i

# target to preprocess a source file
src/NetworkAdapter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.i
.PHONY : src/NetworkAdapter.cpp.i

src/NetworkAdapter.s: src/NetworkAdapter.cpp.s
.PHONY : src/NetworkAdapter.s

# target to generate assembly for a file
src/NetworkAdapter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.s
.PHONY : src/NetworkAdapter.cpp.s

src/NetworkAdapter_Windows.obj: src/NetworkAdapter_Windows.cpp.obj
.PHONY : src/NetworkAdapter_Windows.obj

# target to build an object file
src/NetworkAdapter_Windows.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj
.PHONY : src/NetworkAdapter_Windows.cpp.obj

src/NetworkAdapter_Windows.i: src/NetworkAdapter_Windows.cpp.i
.PHONY : src/NetworkAdapter_Windows.i

# target to preprocess a source file
src/NetworkAdapter_Windows.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.i
.PHONY : src/NetworkAdapter_Windows.cpp.i

src/NetworkAdapter_Windows.s: src/NetworkAdapter_Windows.cpp.s
.PHONY : src/NetworkAdapter_Windows.s

# target to generate assembly for a file
src/NetworkAdapter_Windows.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetwork.dir\build.make CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.s
.PHONY : src/NetworkAdapter_Windows.cpp.s

src/main.obj: src/main.cpp.obj
.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\CheckNetworkDemo.dir\build.make CMakeFiles/CheckNetworkDemo.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... CheckNetwork
	@echo ... CheckNetworkDemo
	@echo ... CheckNetworkTests
	@echo ... SimpleNetworkTest
	@echo ... src/NetworkAdapter.obj
	@echo ... src/NetworkAdapter.i
	@echo ... src/NetworkAdapter.s
	@echo ... src/NetworkAdapter_Windows.obj
	@echo ... src/NetworkAdapter_Windows.i
	@echo ... src/NetworkAdapter_Windows.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

