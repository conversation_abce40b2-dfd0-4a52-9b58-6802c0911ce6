D:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\CheckNetworkTests.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\CheckNetworkTests.dir/objects.a @CMakeFiles\CheckNetworkTests.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\CheckNetworkTests.dir/objects.a -Wl,--no-whole-archive -o CheckNetworkTests.exe -Wl,--out-implib,libCheckNetworkTests.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\CheckNetworkTests.dir\linkLibs.rsp
