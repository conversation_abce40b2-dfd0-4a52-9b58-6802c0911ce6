{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 62, "parent": 0}, {"command": 0, "file": 0, "line": 68, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libCheckNetwork.a"], "targetId": "CheckNetwork::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "bin", "paths": ["CheckNetworkDemo.exe"], "targetId": "CheckNetworkDemo::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": [{"from": "include", "to": "."}], "type": "directory"}], "paths": {"build": ".", "source": "."}}