^E:\WORKSPACE\VSCODE\CHECKNETWORK\BUILD\CMAKEFILES\C3301457DD5FB6047F35DA5040B49CA6\GENERATE.STAMP.RULE
setlocal
D:\msys64\mingw64\bin\cmake.exe -SE:/workspace/vscode/CheckNetwork -BE:/workspace/vscode/CheckNetwork/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/workspace/vscode/CheckNetwork/build/CheckNetwork.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
