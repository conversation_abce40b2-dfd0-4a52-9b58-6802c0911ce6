# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

# Include any dependencies generated for this target.
include tests/CMakeFiles/SimpleNetworkTest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include tests/CMakeFiles/SimpleNetworkTest.dir/compiler_depend.make

# Include the progress variables for this target.
include tests/CMakeFiles/SimpleNetworkTest.dir/progress.make

# Include the compile flags for this target's objects.
include tests/CMakeFiles/SimpleNetworkTest.dir/flags.make

tests/CMakeFiles/SimpleNetworkTest.dir/codegen:
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/codegen

tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj: tests/CMakeFiles/SimpleNetworkTest.dir/flags.make
tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj: tests/CMakeFiles/SimpleNetworkTest.dir/includes_CXX.rsp
tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj: E:/workspace/vscode/CheckNetwork/tests/simple_test.cpp
tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj: tests/CMakeFiles/SimpleNetworkTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj -MF CMakeFiles\SimpleNetworkTest.dir\simple_test.cpp.obj.d -o CMakeFiles\SimpleNetworkTest.dir\simple_test.cpp.obj -c E:\workspace\vscode\CheckNetwork\tests\simple_test.cpp

tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.i"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\CheckNetwork\tests\simple_test.cpp > CMakeFiles\SimpleNetworkTest.dir\simple_test.cpp.i

tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.s"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\CheckNetwork\tests\simple_test.cpp -o CMakeFiles\SimpleNetworkTest.dir\simple_test.cpp.s

# Object files for target SimpleNetworkTest
SimpleNetworkTest_OBJECTS = \
"CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj"

# External object files for target SimpleNetworkTest
SimpleNetworkTest_EXTERNAL_OBJECTS =

tests/SimpleNetworkTest.exe: tests/CMakeFiles/SimpleNetworkTest.dir/simple_test.cpp.obj
tests/SimpleNetworkTest.exe: tests/CMakeFiles/SimpleNetworkTest.dir/build.make
tests/SimpleNetworkTest.exe: libCheckNetwork.a
tests/SimpleNetworkTest.exe: tests/CMakeFiles/SimpleNetworkTest.dir/linkLibs.rsp
tests/SimpleNetworkTest.exe: tests/CMakeFiles/SimpleNetworkTest.dir/objects1.rsp
tests/SimpleNetworkTest.exe: tests/CMakeFiles/SimpleNetworkTest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable SimpleNetworkTest.exe"
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\SimpleNetworkTest.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
tests/CMakeFiles/SimpleNetworkTest.dir/build: tests/SimpleNetworkTest.exe
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/build

tests/CMakeFiles/SimpleNetworkTest.dir/clean:
	cd /d E:\workspace\vscode\CheckNetwork\build\tests && $(CMAKE_COMMAND) -P CMakeFiles\SimpleNetworkTest.dir\cmake_clean.cmake
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/clean

tests/CMakeFiles/SimpleNetworkTest.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\CheckNetwork E:\workspace\vscode\CheckNetwork\tests E:\workspace\vscode\CheckNetwork\build E:\workspace\vscode\CheckNetwork\build\tests E:\workspace\vscode\CheckNetwork\build\tests\CMakeFiles\SimpleNetworkTest.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : tests/CMakeFiles/SimpleNetworkTest.dir/depend

