# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\CheckNetwork

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\CheckNetwork\build

# Include any dependencies generated for this target.
include CMakeFiles/CheckNetwork.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/CheckNetwork.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/CheckNetwork.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/CheckNetwork.dir/flags.make

CMakeFiles/CheckNetwork.dir/codegen:
.PHONY : CMakeFiles/CheckNetwork.dir/codegen

CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj: CMakeFiles/CheckNetwork.dir/flags.make
CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj: CMakeFiles/CheckNetwork.dir/includes_CXX.rsp
CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj: E:/workspace/vscode/CheckNetwork/src/NetworkAdapter.cpp
CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj: CMakeFiles/CheckNetwork.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj -MF CMakeFiles\CheckNetwork.dir\src\NetworkAdapter.cpp.obj.d -o CMakeFiles\CheckNetwork.dir\src\NetworkAdapter.cpp.obj -c E:\workspace\vscode\CheckNetwork\src\NetworkAdapter.cpp

CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\CheckNetwork\src\NetworkAdapter.cpp > CMakeFiles\CheckNetwork.dir\src\NetworkAdapter.cpp.i

CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\CheckNetwork\src\NetworkAdapter.cpp -o CMakeFiles\CheckNetwork.dir\src\NetworkAdapter.cpp.s

CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj: CMakeFiles/CheckNetwork.dir/flags.make
CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj: CMakeFiles/CheckNetwork.dir/includes_CXX.rsp
CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj: E:/workspace/vscode/CheckNetwork/src/NetworkAdapter_Windows.cpp
CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj: CMakeFiles/CheckNetwork.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj -MF CMakeFiles\CheckNetwork.dir\src\NetworkAdapter_Windows.cpp.obj.d -o CMakeFiles\CheckNetwork.dir\src\NetworkAdapter_Windows.cpp.obj -c E:\workspace\vscode\CheckNetwork\src\NetworkAdapter_Windows.cpp

CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\CheckNetwork\src\NetworkAdapter_Windows.cpp > CMakeFiles\CheckNetwork.dir\src\NetworkAdapter_Windows.cpp.i

CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\CheckNetwork\src\NetworkAdapter_Windows.cpp -o CMakeFiles\CheckNetwork.dir\src\NetworkAdapter_Windows.cpp.s

# Object files for target CheckNetwork
CheckNetwork_OBJECTS = \
"CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj" \
"CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj"

# External object files for target CheckNetwork
CheckNetwork_EXTERNAL_OBJECTS =

libCheckNetwork.a: CMakeFiles/CheckNetwork.dir/src/NetworkAdapter.cpp.obj
libCheckNetwork.a: CMakeFiles/CheckNetwork.dir/src/NetworkAdapter_Windows.cpp.obj
libCheckNetwork.a: CMakeFiles/CheckNetwork.dir/build.make
libCheckNetwork.a: CMakeFiles/CheckNetwork.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\CheckNetwork\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX static library libCheckNetwork.a"
	$(CMAKE_COMMAND) -P CMakeFiles\CheckNetwork.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\CheckNetwork.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/CheckNetwork.dir/build: libCheckNetwork.a
.PHONY : CMakeFiles/CheckNetwork.dir/build

CMakeFiles/CheckNetwork.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\CheckNetwork.dir\cmake_clean.cmake
.PHONY : CMakeFiles/CheckNetwork.dir/clean

CMakeFiles/CheckNetwork.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\CheckNetwork E:\workspace\vscode\CheckNetwork E:\workspace\vscode\CheckNetwork\build E:\workspace\vscode\CheckNetwork\build E:\workspace\vscode\CheckNetwork\build\CMakeFiles\CheckNetwork.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/CheckNetwork.dir/depend

