# CMake generated Testfile for 
# Source directory: E:/workspace/vscode/CheckNetwork/tests
# Build directory: E:/workspace/vscode/CheckNetwork/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(SimpleNetworkTest "E:/workspace/vscode/CheckNetwork/build/tests/SimpleNetworkTest.exe")
set_tests_properties(SimpleNetworkTest PROPERTIES  _BACKTRACE_TRIPLES "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;12;add_test;E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;0;")
add_test(NetworkAdapterTests "E:/workspace/vscode/CheckNetwork/build/tests/CheckNetworkTests.exe")
set_tests_properties(NetworkAdapterTests PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;35;add_test;E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;0;")
