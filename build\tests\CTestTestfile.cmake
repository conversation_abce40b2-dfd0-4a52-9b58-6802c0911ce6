# CMake generated Testfile for 
# Source directory: E:/workspace/vscode/CheckNetwork/tests
# Build directory: E:/workspace/vscode/CheckNetwork/build/tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
if(CTEST_CONFIGURATION_TYPE MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
  add_test(NetworkAdapterTests "E:/workspace/vscode/CheckNetwork/build/tests/Debug/CheckNetworkTests.exe")
  set_tests_properties(NetworkAdapterTests PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;28;add_test;E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
  add_test(NetworkAdapterTests "E:/workspace/vscode/CheckNetwork/build/tests/Release/CheckNetworkTests.exe")
  set_tests_properties(NetworkAdapterTests PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;28;add_test;E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
  add_test(NetworkAdapterTests "E:/workspace/vscode/CheckNetwork/build/tests/MinSizeRel/CheckNetworkTests.exe")
  set_tests_properties(NetworkAdapterTests PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;28;add_test;E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;0;")
elseif(CTEST_CONFIGURATION_TYPE MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
  add_test(NetworkAdapterTests "E:/workspace/vscode/CheckNetwork/build/tests/RelWithDebInfo/CheckNetworkTests.exe")
  set_tests_properties(NetworkAdapterTests PROPERTIES  TIMEOUT "30" _BACKTRACE_TRIPLES "E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;28;add_test;E:/workspace/vscode/CheckNetwork/tests/CMakeLists.txt;0;")
else()
  add_test(NetworkAdapterTests NOT_AVAILABLE)
endif()
